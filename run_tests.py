#!/usr/bin/env python3
"""
Test runner for CV Duplicate Checker Agent

Usage:
    python run_tests.py                 # Run all tests
    python run_tests.py --basic         # Run basic test
    python run_tests.py --comprehensive # Run comprehensive tests
    python run_tests.py --performance   # Run performance test only
"""

import asyncio
import sys
import os
import argparse

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from examples.basic_usage import main as example_usage
from tests.test_comprehensive import TestCVDuplicate<PERSON>hecker
from tests.test_semantic_matching import main as run_semantic_tests


async def run_basic_test():
    """Run the basic test from test_agent.py"""
    print("="*60)
    print("RUNNING BASIC TEST")
    print("="*60)
    await example_usage()


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("="*60)
    print("RUNNING COMPREHENSIVE TESTS")
    print("="*60)
    
    test_instance = TestCVDuplicateChecker()
    
    tests = [
        ("Empty CV - Add New Data", test_instance.test_empty_cv_add_new_data),
        ("Exact Duplicate Skip", test_instance.test_exact_duplicate_skip),
        ("Partial Update Missing Fields", test_instance.test_partial_update_missing_fields),
        ("Complex Experience Matching", test_instance.test_complex_experience_matching),
        ("All Sections Comprehensive", test_instance.test_all_sections_comprehensive),
        ("Special Characters and Unicode", test_instance.test_special_characters_and_unicode),
        ("Edge Cases", test_instance.test_edge_cases),
        ("Performance Large Dataset", test_instance.test_performance_large_dataset),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            test_instance.setup_method()
            test_func()
            print(f"✅ {test_name} - PASSED")
            passed += 1
        except AssertionError as e:
            print(f"❌ {test_name} - FAILED: {str(e)}")
            failed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"COMPREHENSIVE TESTS SUMMARY")
    print(f"Total: {len(tests)} | Passed: {passed} | Failed: {failed}")
    print(f"{'='*60}")
    
    return failed == 0


def run_performance_test():
    """Run performance test only"""
    print("="*60)
    print("RUNNING PERFORMANCE TEST")
    print("="*60)
    
    test_instance = TestCVDuplicateChecker()
    try:
        test_instance.setup_method()
        test_instance.test_performance_large_dataset()
        print("✅ Performance Large Dataset - PASSED")
        return True
    except Exception as e:
        print(f"❌ Performance Large Dataset - FAILED: {str(e)}")
        return False


async def main():
    parser = argparse.ArgumentParser(description="CV Duplicate Checker Test Runner")
    parser.add_argument("--basic", action="store_true", help="Run basic test only")
    parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive tests only")
    parser.add_argument("--semantic", action="store_true", help="Run semantic matching tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance test only")
    
    args = parser.parse_args()
    
    success = True
    
    if args.basic:
        await run_basic_test()
    elif args.comprehensive:
        success = run_comprehensive_tests()
    elif args.semantic:
        print("="*60)
        print("RUNNING SEMANTIC MATCHING TESTS")
        print("="*60)
        await run_semantic_tests()
    elif args.performance:
        success = run_performance_test()
    else:
        # Run all tests
        print("🚀 Running All Tests for CV Duplicate Checker Agent\n")
        
        # Basic test
        await run_basic_test()
        print("\n")
        
        # Comprehensive tests
        success = run_comprehensive_tests()
        print("\n")
        
        # Semantic matching tests
        print("="*60)
        print("RUNNING SEMANTIC MATCHING TESTS")
        print("="*60)
        await run_semantic_tests()
        
        if success:
            print("\n🎉 ALL TESTS PASSED!")
        else:
            print("\n💥 SOME TESTS FAILED!")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())