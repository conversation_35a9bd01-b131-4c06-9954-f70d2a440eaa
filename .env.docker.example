# Docker Environment Configuration for CV Duplicate Checker
# Copy this file to .env and fill in your values

# ========================================
# REQUIRED: OpenAI Configuration
# ========================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# ========================================
# Optional: OpenAI Settings
# ========================================
# OpenAI model to use (default: gpt-4o-mini)
OPENAI_MODEL=gpt-4o-mini

# Temperature for LLM responses (0 = deterministic, 1 = creative)
OPENAI_TEMPERATURE=0

# ========================================
# Optional: API Server Configuration
# ========================================
# Port to expose the API on your host machine
API_PORT=8000

# Log level for the application
API_LOG_LEVEL=info

# Enable auto-reload for development (true/false)
API_RELOAD=false

# Enable debug mode (true/false)
DEBUG=false

# ========================================
# Docker Usage Examples
# ========================================
# 
# Production:
# docker-compose up -d
#
# Development with live reload:
# docker-compose --profile dev up
#
# Build and run:
# docker-compose up --build
#
# View logs:
# docker-compose logs -f cv-duplicate-checker
#
# Stop services:
# docker-compose down
#
# ========================================