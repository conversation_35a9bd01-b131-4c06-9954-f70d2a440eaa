# CV Duplicate Checker Agent

An intelligent CV duplicate detection and merging system powered by LangGraph and OpenAI's LLM for semantic understanding.

## Features

- **Semantic Matching**: Uses LLM to understand that "DHBK" equals "Đại họ<PERSON>"
- **Async Processing**: Improved concurrency with async agent processing
- **Parallel Processing**: All CV sections processed simultaneously using LangGraph
- **Multi-language Support**: Handles Vietnamese ↔ English translations seamlessly
- **Smart Actions**: ADD new info, UPDATE missing fields, or SKIP complete duplicates
- **Memory Leak Prevention**: Comprehensive memory management and leak detection system
- **REST API**: Production-ready FastAPI wrapper with automatic documentation
- **Web Interface**: Interactive web UI at `/web` endpoint
- **Reliable**: Graceful fallbacks if LLM unavailable
- **Comprehensive**: Handles 10 CV sections (education, experience, certifications, etc.)
- **Interactive Docs**: Auto-generated Swagger/OpenAPI documentation
- **Performance Monitoring**: Built-in memory profiling and load testing tools

## Quick Start

### Prerequisites

- Python 3.13+
- [uv](https://docs.astral.sh/uv/) package manager (install with: `curl -LsSf https://astral.sh/uv/install.sh | sh`)
- OpenAI API key

### Installation

1. **Clone the repository**

```bash
git clone <repository-url>
cd upzi-cv-duplicate-checker
```

2. **Install dependencies with uv**

```bash
uv sync
```

3. **Activate virtual environment**

```bash
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

4. **Set up environment variables**

```bash
# Copy example configuration
cp .env.example .env

# Edit .env file with your API key
# Required: Update OPENAI_API_KEY with your actual OpenAI API key
```

5. **Verify installation**

```bash
uv run python run_tests.py --basic
```

## 🐳 Docker Setup (Recommended)

For a quick setup without installing Python dependencies locally:

### Prerequisites
- [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/install/)
- OpenAI API key

### Quick Start with Docker

1. **Clone and setup**
```bash
git clone <repository-url>
cd upzi-cv-duplicate-checker

# Copy environment template
cp .env.docker.example .env

# Edit .env and add your OPENAI_API_KEY
nano .env
```

2. **Run with Docker Compose**
```bash
# Production mode
docker-compose up -d

# Development mode (with live reload)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

3. **Access the application**
- **Web Interface**: http://localhost:8000/web
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

📖 **See [DOCKER.md](DOCKER.md) for complete Docker documentation**
🏗️ **Architecture & Development**: See [CLAUDE.md](CLAUDE.md) for detailed development guidance

### Basic Usage

### Option 1: Python Package

```python
from src.cv_duplicate_checker import CVDuplicateCheckerAgent
import json

# Initialize the agent
agent = CVDuplicateCheckerAgent()

# Prepare your data
existing_cv = {
    "education": [
        {"school": "DHBK", "major": "Computer Science", "start_date": "2020"}
    ],
    "experience": [
        {"title": "Software Engineer", "company": "Google", "start_date": "2022-01-01"}
    ],
    "extra_curricular": [],
    "certification": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
}

new_data = {
    "education": [
        {
            "school": "Đại học Bách Khoa", 
            "major": "Computer Science", 
            "start_date": "2020",
            "degree": "Bachelor",
            "gpa": 3.8
        }
    ],
    "experience": [
        {
            "title": "Software Engineer", 
            "company": "Google LLC", 
            "start_date": "2022-01-01",
            "end_date": "2023-12-31",
            "skills": ["Python", "Go"]
        }
    ]
}

# Process the data
result = agent.process(
    json.dumps(existing_cv, ensure_ascii=False),
    json.dumps(new_data, ensure_ascii=False)
)

# Parse and display results
result_dict = json.loads(result)
print("Actions taken:", result_dict["action_summary"])
print("Processed CV:", result_dict["processed_cv"])
```

### Option 2: REST API

#### Start the API Server

```bash
# Development mode (auto-reload, saves config to .env)
python main.py --reload

# Production mode (saves config to .env)
python main.py --host 0.0.0.0 --port 8000

# Custom configuration (saves to .env)
python main.py --host 127.0.0.1 --port 8080 --log-level debug

# Use existing .env configuration
python main.py

# Show current configuration
python main.py --config

# Temporary override without saving to .env
python main.py --host 0.0.0.0 --port 8080 --no-save
```

#### API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | API information and available endpoints |
| `GET` | `/health` | Health check and service status |
| `GET` | `/web` | Interactive web interface for CV processing |
| `POST` | `/check-duplicates` | Main CV duplicate checking endpoint |
| `GET` | `/docs` | Interactive Swagger UI documentation |
| `GET` | `/redoc` | Alternative ReDoc documentation |

#### Health Check

```bash
curl http://127.0.0.1:8000/health
```

```json
{
  "status": "healthy",
  "version": "1.0.0", 
  "agent_ready": true
}
```

#### CV Duplicate Check Request

**Endpoint:** `POST /check-duplicates`

**Request Body:**

```json
{
  "existing_cv": {
    "education": [
      {
        "school": "DHBK",
        "major": "Computer Science", 
        "start_date": "2020"
      }
    ],
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Google",
        "start_date": "2022-01-01"
      }
    ],
    "extra_curricular": [],
    "certification": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  },
  "new_data": {
    "education": [
      {
        "school": "Đại học Bách Khoa",
        "major": "Computer Science",
        "start_date": "2020",
        "degree": "Bachelor",
        "gpa": 3.8
      }
    ],
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Google LLC",
        "start_date": "2022-01-01",
        "end_date": "2023-12-31",
        "skills": ["Python", "Go"]
      }
    ],
    "extra_curricular": [],
    "certification": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  }
}
```

**Response:**

```json
{
  "processed_cv": {
    "education": [
      {
        "school": "Đại học Bách Khoa",
        "major": "Computer Science",
        "start_date": "2020",
        "degree": "Bachelor",
        "gpa": 3.8
      }
    ],
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Google LLC",
        "start_date": "2022-01-01",
        "end_date": "2023-12-31",
        "skills": ["Python", "Go"]
      }
    ]
  },
  "action_summary": {
    "total_actions": 2,
    "added": 0,
    "updated": 2,
    "skipped": 0
  },
  "section_summary": {
    "education": {
      "added": 0,
      "updated": 1,
      "skipped": 0
    },
    "experience": {
      "added": 0, 
      "updated": 1,
      "skipped": 0
    }
  },
  "detailed_actions": [
    {
      "action": "update",
      "item": {
        "school": "Đại học Bách Khoa",
        "major": "Computer Science",
        "start_date": "2020",
        "degree": "Bachelor",
        "gpa": 3.8
      },
      "existing_index": 0,
      "fields_to_update": ["degree", "gpa"],
      "reason": "Bổ sung thông tin thiếu: degree, gpa (phát hiện bằng semantic matching)",
      "section": "education"
    }
  ]
}
```

#### Python Client Example

```python
import requests

# API configuration
API_BASE_URL = "http://127.0.0.1:8000"

# Prepare request data
request_data = {
    "existing_cv": {
        "education": [{"school": "DHBK", "major": "Computer Science", "start_date": "2020"}],
        "experience": [{"title": "Software Engineer", "company": "Google", "start_date": "2022-01-01"}],
        "extra_curricular": [],
        "certification": [],
        "language": [],
        "achievement": [],
        "reference": [],
        "external_doc": [],
        "summary": [],
        "hobby": []
    },
    "new_data": {
        "education": [
            {
                "school": "Đại học Bách Khoa",  # Semantic match with "DHBK"
                "major": "Computer Science",
                "start_date": "2020",
                "degree": "Bachelor",
                "gpa": 3.8
            }
        ],
        "experience": [
            {
                "title": "Software Engineer",
                "company": "Google LLC",  # Semantic match with "Google"
                "start_date": "2022-01-01",
                "end_date": "2023-12-31",
                "skills": ["Python", "Go"]
            }
        ],
        "extra_curricular": [],
        "certification": [],
        "language": [],
        "achievement": [],
        "reference": [],
        "external_doc": [],
        "summary": [],
        "hobby": []
    }
}

# Send request
response = requests.post(
    f"{API_BASE_URL}/check-duplicates",
    json=request_data,
    headers={"Content-Type": "application/json"},
    timeout=30
)

if response.status_code == 200:
    result = response.json()
    print("✅ Success!")
    print(f"Total actions: {result['action_summary']['total_actions']}")
    print(f"Added: {result['action_summary']['added']}")
    print(f"Updated: {result['action_summary']['updated']}")
    print(f"Skipped: {result['action_summary']['skipped']}")
else:
    print(f"❌ Error: {response.status_code}")
    print(response.json())
```

#### cURL Example

```bash
curl -X POST "http://127.0.0.1:8000/check-duplicates" \
  -H "Content-Type: application/json" \
  -d '{
    "existing_cv": {
      "education": [{"school": "DHBK", "major": "Computer Science", "start_date": "2020"}],
      "experience": [],
      "extra_curricular": [],
      "certification": [],
      "language": [],
      "achievement": [],
      "reference": [],
      "external_doc": [],
      "summary": [],
      "hobby": []
    },
    "new_data": {
      "education": [
        {
          "school": "Đại học Bách Khoa",
          "major": "Computer Science",
          "start_date": "2020",
          "degree": "Bachelor",
          "gpa": 3.8
        }
      ],
      "experience": [],
      "extra_curricular": [],
      "certification": [],
      "language": [],
      "achievement": [],
      "reference": [],
      "external_doc": [],
      "summary": [],
      "hobby": []
    }
  }'
```

#### Error Handling

The API returns appropriate HTTP status codes:

- **200 OK**: Successful processing
- **400 Bad Request**: Invalid input data
- **500 Internal Server Error**: Processing error
- **503 Service Unavailable**: Agent not ready

Example error response:

```json
{
  "error": "ValidationError",
  "message": "Invalid CV data provided",
  "details": {
    "field": "education",
    "issue": "Missing required field 'school'"
  }
}
```

#### Interactive API Documentation

Once the API is running, visit:

- **Swagger UI**: http://127.0.0.1:8000/docs
- **ReDoc**: http://127.0.0.1:8000/redoc
- **Health Check**: http://127.0.0.1:8000/health
- **API Info**: http://127.0.0.1:8000/

#### Rate Limiting & Production Considerations

For production deployment, consider:

- **Reverse Proxy**: Use nginx or similar for load balancing
- **Rate Limiting**: Implement API rate limiting
- **Authentication**: Add API key or OAuth authentication
- **Monitoring**: Set up health checks and logging
- **Environment**: Set `OPENAI_API_KEY` environment variable
- **Scaling**: Use multiple worker processes with gunicorn

## Agent Workflow

### High-Level Process

```mermaid
flowchart TD
    A[Existing CV JSON] --> D[CV Duplicate Checker Agent]
    B[New Data JSON] --> D
    
    D --> E[Parse Input & Initialize State]
    E --> F[Parallel Section Processing]
    
    F --> G1[Education]
    F --> G2[Experience] 
    F --> G3[Extra-curricular]
    F --> G4[Certification]
    F --> G5[Language]
    F --> G6[Achievement]
    F --> G7[Reference]
    F --> G8[External Doc]
    F --> G9[Summary]
    F --> G10[Hobby]
    
    G1 --> H[Semantic Duplicate Detection]
    G2 --> H
    G3 --> H
    G4 --> H
    G5 --> H
    G6 --> H
    G7 --> H
    G8 --> H
    G9 --> H
    G10 --> H
    
    H --> I[LLM Analysis]
    I --> J[Merge Results]
    J --> K[Final Output]
```

### Semantic Matching Engine

The agent uses OpenAI's LLM to understand semantic similarities:

```mermaid
flowchart TD
    A[Compare Two Values] --> B{Exact Match?}
    
    B -->|Yes| C[Return True]
    B -->|No| D{Text Fields?}
    
    D -->|No| E[Exact Comparison Only]
    D -->|Yes| F[Determine Field Context]
    
    F --> G[Call LLM with Context]
    G --> I{LLM Decision}
    I -->|"true"| J[Semantic Match Found]
    I -->|"false"| K[No Semantic Match]
    I -->|Error| L[Fallback to Exact Match]
```

### Action Types

| Action | Description | Example |
|--------|-------------|---------|
| **ADD** | Completely new information | New job experience |
| **UPDATE** | Existing entry with missing fields | Add GPA to existing education |
| **SKIP** | Complete duplicate found | Identical certification |

## Testing

### Run All Tests

```bash
uv run python run_tests.py
```

### Run Specific Test Suites

```bash
# Basic functionality test
uv run python run_tests.py --basic

# Comprehensive edge cases
uv run python run_tests.py --comprehensive

# Semantic matching validation
uv run python run_tests.py --semantic

# Performance with large datasets
uv run python run_tests.py --performance
```

### Memory Leak Detection and Performance Testing

The system now includes comprehensive memory management and monitoring tools:

```bash
# Run memory profiling tests
uv run python tools/test_memory_profiler.py

# Load testing with memory monitoring
uv run python tools/load_test_memory.py
```

**Memory Management Features:**
- Automatic memory leak detection and prevention
- Memory usage monitoring during processing
- Garbage collection optimization
- Resource cleanup for long-running processes
- Performance profiling tools for development

### Test the API

#### Full API Test Suite

```bash
# Terminal 1: Start the API server
python main.py --reload

# Terminal 2: Run comprehensive API tests
uv run python examples/api_client_example.py
```

The API client example tests:

- ✅ **Health Check**: Verify API is running and agent is ready
- ✅ **Semantic Matching**: Test DHBK ↔ Đại học Bách Khoa equivalence
- ✅ **CV Processing**: Complete duplicate detection workflow
- ✅ **Edge Cases**: Empty new_data results in 0 total actions
- ✅ **Error Handling**: Proper status codes and error messages

#### Quick API Health Check

```bash
# Check if API is running
curl http://127.0.0.1:8000/health

# Expected response:
# {"status": "healthy", "version": "1.0.0", "agent_ready": true}
```

#### Manual API Testing

```bash
# Test basic functionality
curl -X POST "http://127.0.0.1:8000/check-duplicates" \
  -H "Content-Type: application/json" \
  -d @examples/sample_request.json
```

### Test Coverage

- **8 Comprehensive Test Cases**: All scenarios covered with async support
- **5 Semantic Matching Tests**: LLM validation with improved error handling
- **100% Pass Rate**: All tests consistently passing
- **Performance Testing**: Large dataset handling (50+ entries)
- **Memory Testing**: Memory leak detection and profiling
- **Load Testing**: High-concurrency request handling

## Supported CV Sections

| Section | Duplicate Criteria | Updatable Fields |
|---------|-------------------|------------------|
| **Education** | school + major | start_date, end_date, degree, description, favorite_subjects, gpa |
| **Experience** | title + company + start_date | description, end_date, skills |
| **Certification** | certification_name + organization | issue_date, expire_date, url |
| **Language** | language_name | level |
| **Achievement** | title + organization + issue_date | description |
| **Reference** | email | name, position, company |
| **External Doc** | link | title, description |
| **Summary** | summary | (no updates) |
| **Hobby** | hobby | description |
| **Extra-curricular** | role + organization + start_date | end_date, description, skills |

## Semantic Matching Examples

The agent understands these semantic equivalences:

### Educational Institutions

- `DHBK` ↔ `Đại học Bách Khoa`
- `MIT` ↔ `Massachusetts Institute of Technology`
- `HUST` ↔ `Hanoi University of Science and Technology`

### Companies

- `Google` ↔ `Google LLC`
- `Microsoft` ↔ `Microsoft Corporation`
- `Meta` ↔ `Facebook Inc.`

### Certifications

- `AWS Developer` ↔ `AWS Certified Developer Associate`
- `PMP` ↔ `Project Management Professional`

### Academic Fields

- `Computer Science` ↔ `Khoa học máy tính`
- `Business Administration` ↔ `Quản trị kinh doanh`

## Configuration

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
OPENAI_MODEL=gpt-4o-mini  # Default model
OPENAI_TEMPERATURE=0      # Default temperature

# API Server (optional)
DEBUG=false               # Enable debug mode
```

### Agent Parameters

```python
# Python package usage
agent = CVDuplicateCheckerAgent(
    model_name="gpt-4o-mini",  # OpenAI model to use
    temperature=0              # Temperature for consistency
)
```

### API Server Configuration

The API server uses a combination of `.env` file and command-line arguments for configuration:

```bash
# All command-line options are automatically saved to .env file
python main.py \
  --host 127.0.0.1 \        # Server host (default: 127.0.0.1)
  --port 8000 \              # Server port (default: 8000)
  --reload \                 # Auto-reload on code changes
  --log-level info           # Logging level (debug, info, warning, error)

# View current configuration
python main.py --config

# Use configuration from .env file only
python main.py

# Temporary override without saving
python main.py --host 0.0.0.0 --no-save
```

#### .env File Configuration

The `.env` file stores persistent API configuration. Start by copying the example:

```bash
cp .env.example .env
```

Key configuration options:

```bash
# API Server Configuration (auto-generated by main.py)
API_HOST=127.0.0.1
API_PORT=8000
API_RELOAD=true
API_LOG_LEVEL=info

# Required for LLM functionality
OPENAI_API_KEY=your_openai_api_key_here

# Optional OpenAI settings
OPENAI_MODEL=gpt-4o-mini
OPENAI_TEMPERATURE=0

# Optional debug mode
DEBUG=false
```

The `.env.example` file contains comprehensive documentation for all available configuration options including:
- **API Server Settings**: Host, port, reload, logging
- **OpenAI Configuration**: Model, temperature, max tokens
- **Performance Settings**: Caching, rate limiting
- **Security Settings**: API keys, authentication
- **Production Settings**: Workers, timeouts
- **Example Configurations**: Development vs production setups

#### Production Deployment

```bash
# Production with Gunicorn
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  -b 0.0.0.0:8000 \
  src.cv_duplicate_checker.api:app

# Or use main.py for production
python main.py --host 0.0.0.0 --port 8000 --log-level warning
```

## Project Structure

```text
upzi-cv-duplicate-checker/
├── src/
│   └── cv_duplicate_checker/
│       ├── __init__.py          # Package initialization
│       ├── models.py            # Pydantic models for CV data
│       ├── agent.py             # Main agent implementation
│       ├── api_models.py        # FastAPI request/response models
│       └── api.py               # FastAPI application
├── examples/
│   ├── __init__.py
│   ├── basic_usage.py           # Basic usage example
│   └── api_client_example.py    # API client example
├── tests/
│   ├── test_comprehensive.py   # Comprehensive test suite
│   └── test_semantic_matching.py # Semantic matching tests
├── tools/
│   ├── test_memory_profiler.py # Memory profiling and leak detection
│   └── load_test_memory.py     # Load testing with memory monitoring
├── docs/
│   ├── architecture_diagram.md # Detailed architecture docs
│   ├── workflow_diagram.md     # Workflow diagrams
│   ├── README_ARCHITECTURE.md  # Complete architecture guide
│   └── MEMORY_LEAK_DETECTION_PLAN.md  # Memory management documentation
├── CLAUDE.md                   # Development guidance for Claude Code
├── main.py                     # API server startup script with .env management
├── run_tests.py                # Test runner script
├── .env                       # Environment variables (created from .env.example)
├── .env.example               # Environment variables template
├── pyproject.toml            # Project configuration
├── uv.lock                   # Dependency lock file
└── README.md                 # This file
```

## Performance

- **Async Processing**: Improved concurrency with async agent processing
- **Parallel Processing**: All 10 CV sections process simultaneously
- **Smart LLM Usage**: Only calls LLM for text field comparisons
- **Memory Efficient**: Section-specific state management with leak prevention
- **Reliable Fallbacks**: Graceful degradation if LLM unavailable
- **Resource Management**: Automatic cleanup and garbage collection optimization
- **Load Testing**: Built-in tools for performance validation

## Development

### Adding New CV Sections

1. **Define Pydantic Model** (`src/cv_duplicate_checker/models.py`)

```python
class NewSection(BaseModel):
    field1: str = Field(description="Description")
    field2: Optional[str] = Field(default=None, description="Optional field")
```

2. **Add to ProcessedCV** (`src/cv_duplicate_checker/models.py`)

```python
class ProcessedCV(BaseModel):
    new_section: Optional[List[NewSection]] = Field(default=None)
```

3. **Add Check Method** (`src/cv_duplicate_checker/agent.py`)

```python
def _check_new_section(self, state: AgentState) -> Dict[str, Any]:
    # Implementation similar to other check methods
    pass
```

4. **Update Graph Workflow** (`src/cv_duplicate_checker/agent.py`)

```python
workflow.add_node("check_new_section", self._check_new_section)
workflow.add_edge("parse_input", "check_new_section")
workflow.add_edge("check_new_section", "merge_results")
```

5. **Update API Models** (`src/cv_duplicate_checker/api_models.py`)

```python
class CVData(BaseModel):
    # Add new section
    new_section: Optional[List[Dict[str, Any]]] = Field(default=None, description="New section entries")
```

### Extending the API

#### Adding New Endpoints

```python
# src/cv_duplicate_checker/api.py
@app.post("/analyze-skills", response_model=SkillAnalysisResponse)
async def analyze_skills(request: SkillAnalysisRequest):
    """Analyze skills from CV data"""
    # Implementation here
    pass
```

#### Custom Middleware

```python
@app.middleware("http")
async def api_key_middleware(request: Request, call_next):
    """Add API key authentication"""
    api_key = request.headers.get("X-API-Key")
    if not api_key or not validate_api_key(api_key):
        return JSONResponse(
            status_code=401,
            content={"error": "Invalid API key"}
        )
    response = await call_next(request)
    return response
```

#### Response Caching

```python
from functools import lru_cache

@lru_cache(maxsize=100)
def cached_cv_processing(cv_hash: str):
    """Cache CV processing results"""
    pass
```

### Improving Semantic Matching

Enhance the `_is_semantically_similar` method by:

- **Context-specific prompts**: Different prompts for education vs experience
- **Caching**: Store common semantic matches to reduce API calls
- **Custom similarity scoring**: Implement confidence scores
- **Multi-language support**: Enhanced Vietnamese ↔ English matching

```python
# Example enhancement
def _is_semantically_similar(self, value1: str, value2: str, field_type: str = "general") -> bool:
    # Check cache first
    cache_key = f"{value1}|{value2}|{field_type}"
    if cache_key in self.similarity_cache:
        return self.similarity_cache[cache_key]
    
    # Context-specific prompts
    prompts = {
        "school": "Compare these educational institution names...",
        "company": "Compare these company/organization names...",
        "certification": "Compare these certification names..."
    }
    
    # Use appropriate prompt and cache result
    result = self._llm_similarity_check(value1, value2, prompts.get(field_type, "general"))
    self.similarity_cache[cache_key] = result
    return result
```

### API Testing and Development

#### Development Workflow

```bash
# 1. Start API in development mode
python main.py --reload --log-level debug

# 2. Run tests in another terminal  
uv run python examples/api_client_example.py

# 3. View interactive docs
open http://127.0.0.1:8000/docs

# 4. Monitor logs and iterate
```

#### Adding API Tests

```python
# tests/test_api.py
import pytest
from fastapi.testclient import TestClient
from src.cv_duplicate_checker.api import app

client = TestClient(app)

def test_health_endpoint():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_check_duplicates():
    request_data = {...}  # Your test data
    response = client.post("/check-duplicates", json=request_data)
    assert response.status_code == 200
    assert "action_summary" in response.json()
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license here]

## Support

For questions or issues:

- Create an issue in the repository
- Check the architecture documentation in `README_ARCHITECTURE.md`
- Review test examples in the test files

---

**Built with ❤️ using LangChain, LangGraph, and OpenAI**