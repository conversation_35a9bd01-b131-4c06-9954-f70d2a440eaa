#!/usr/bin/env python3
"""
Main startup script for CV Duplicate Checker API

Usage:
    python main.py                      # Use .env file configuration
    python main.py --host 0.0.0.0      # Override host and save to .env
    python main.py --port 8080         # Override port and save to .env
    python main.py --reload             # Enable auto-reload for development
    python main.py --config             # Show current configuration
"""

import argparse
import os
import sys
from pathlib import Path

import uvicorn
from dotenv import load_dotenv, set_key

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Default configuration
DEFAULT_CONFIG = {
    "API_HOST": "127.0.0.1",
    "API_PORT": "8000",
    "API_RELOAD": "false",
    "API_LOG_LEVEL": "info",
}


def get_env_file_path():
    """Get the path to the .env file"""
    return Path(__file__).parent / ".env"


def load_config():
    """Load configuration from .env file"""
    env_file = get_env_file_path()
    
    # Load existing .env file
    load_dotenv(env_file)
    
    # Get values with defaults
    config = {}
    for key, default_value in DEFAULT_CONFIG.items():
        config[key] = os.getenv(key, default_value)
    
    return config


def save_config_to_env(args):
    """Save command line arguments to .env file"""
    env_file = get_env_file_path()
    
    # Ensure .env file exists
    env_file.touch(exist_ok=True)
    
    # Update .env file with new values
    if args.host:
        set_key(env_file, "API_HOST", args.host)
        print(f"✅ Updated API_HOST={args.host} in .env")
    
    if args.port:
        set_key(env_file, "API_PORT", str(args.port))
        print(f"✅ Updated API_PORT={args.port} in .env")
    
    if args.reload is not None:
        set_key(env_file, "API_RELOAD", str(args.reload).lower())
        print(f"✅ Updated API_RELOAD={str(args.reload).lower()} in .env")
    
    if args.log_level:
        set_key(env_file, "API_LOG_LEVEL", args.log_level)
        print(f"✅ Updated API_LOG_LEVEL={args.log_level} in .env")


def show_config():
    """Display current configuration"""
    config = load_config()
    
    print("📋 Current API Configuration:")
    print("=" * 40)
    for key, value in config.items():
        env_key = key
        print(f"{env_key}: {value}")
    print("=" * 40)
    
    # Check for OPENAI_API_KEY
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key:
        print(f"OPENAI_API_KEY: {'*' * 20}...{openai_key[-4:] if len(openai_key) > 4 else '****'}")
    else:
        print("OPENAI_API_KEY: ❌ Not set")
    print()


def main():
    # Parse command line arguments first
    parser = argparse.ArgumentParser(description="CV Duplicate Checker API Server")
    parser.add_argument("--host", help="Host to bind to (saves to .env)")
    parser.add_argument("--port", type=int, help="Port to bind to (saves to .env)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development (saves to .env)")
    parser.add_argument("--log-level", help="Log level: debug, info, warning, error (saves to .env)")
    parser.add_argument("--config", action="store_true", help="Show current configuration and exit")
    parser.add_argument("--no-save", action="store_true", help="Don't save arguments to .env file")

    args = parser.parse_args()

    # Load configuration from .env file
    config = load_config()

    # Show configuration if requested
    if args.config:
        show_config()
        return

    # Save arguments to .env file (unless --no-save is specified)
    if not args.no_save and any([args.host, args.port, args.reload, args.log_level]):
        save_config_to_env(args)
        # Reload config after saving
        config = load_config()

    # Use command line args or fall back to .env values
    host = args.host or config["API_HOST"]
    port = int(args.port or config["API_PORT"])
    reload = args.reload if args.reload else config["API_RELOAD"].lower() == "true"
    log_level = args.log_level or config["API_LOG_LEVEL"]

    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable is required")
        print("Please set your OpenAI API key in .env file:")
        print("   echo 'OPENAI_API_KEY=your_api_key_here' >> .env")
        print()
        show_config()
        sys.exit(1)

    print("🚀 Starting CV Duplicate Checker API...")
    print(f"📡 Server: http://{host}:{port}")
    print(f"📚 API Docs: http://{host}:{port}/docs")
    print(f"💊 Health: http://{host}:{port}/health")
    print(f"🔧 Reload: {'Enabled' if reload else 'Disabled'}")
    print(f"📝 Log Level: {log_level}")
    print()

    # Run the API
    uvicorn.run(
        "cv_duplicate_checker.api:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True,
    )


if __name__ == "__main__":
    main()
