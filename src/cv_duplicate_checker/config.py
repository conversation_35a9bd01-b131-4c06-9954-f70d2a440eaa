"""Configuration constants and settings for CV Duplicate Checker"""

import os
from typing import List

# API Configuration
DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 8000
DEFAULT_LOG_LEVEL = "info"

# Agent Configuration
AGENT_REFRESH_THRESHOLD = int(os.getenv("AGENT_REFRESH_THRESHOLD", "500"))  # Reduced from 1000
LLM_TIMEOUT = int(os.getenv("LLM_TIMEOUT", "30"))
LLM_MAX_RETRIES = int(os.getenv("LLM_MAX_RETRIES", "2"))
DEFAULT_MODEL = "gpt-4o-mini"
DEFAULT_TEMPERATURE = 0.0

# Memory Management
MAX_ACTIONS_PER_SECTION = int(os.getenv("MAX_ACTIONS_PER_SECTION", "100"))
MAX_TOTAL_ACTIONS = int(os.getenv("MAX_TOTAL_ACTIONS", "1000"))

# Security Configuration
ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8000").split(",")
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1,0.0.0.0").split(",")
MAX_REQUEST_SIZE = int(os.getenv("MAX_REQUEST_SIZE", "10485760"))  # 10MB

# Semantic Matching Configuration
SEMANTIC_CACHE_SIZE = int(os.getenv("SEMANTIC_CACHE_SIZE", "1000"))
SEMANTIC_CACHE_TTL = int(os.getenv("SEMANTIC_CACHE_TTL", "3600"))  # 1 hour

# CV Section Configuration
CV_SECTIONS = [
    "education",
    "experience", 
    "extra_curricular",
    "certification",
    "language",
    "achievement",
    "reference",
    "external_doc",
    "summary",
    "hobby"
]

# Duplicate Criteria Configuration
DUPLICATE_CRITERIA = {
    "education": ["school", "major", "start_date"],
    "experience": ["title", "company", "start_date"],
    "extra_curricular": ["role", "organization", "start_date"],
    "certification": ["certification_name", "organization"],
    "language": ["language_name"],
    "achievement": ["title", "organization", "issue_date"],
    "reference": ["email"],
    "external_doc": ["link"],
    "summary": ["summary"],
    "hobby": ["hobby"]
}

# Updatable Fields Configuration
UPDATABLE_FIELDS = {
    "education": ["description", "end_date", "degree", "gpa", "favorite_subjects"],
    "experience": ["description", "end_date", "skills"],
    "extra_curricular": ["description", "end_date"],
    "certification": ["issue_date", "expire_date", "url"],
    "language": ["level"],
    "achievement": ["description"],
    "reference": ["name", "position", "company", "phone"],
    "external_doc": ["title", "description"],
    "summary": [],
    "hobby": ["description"]
}

# Field Type Mapping for Semantic Matching
FIELD_TYPE_MAPPING = {
    "school": "school",
    "university": "school",
    "company": "company", 
    "organization": "company",
    "major": "major",
    "field_of_study": "major",
    "title": "position",
    "position": "position",
    "role": "position",
    "certification_name": "certification"
}

# Error Messages
ERROR_MESSAGES = {
    "agent_init_failed": "Failed to initialize CV Duplicate Checker Agent",
    "invalid_input": "Invalid CV data provided",
    "processing_failed": "Failed to process CV data",
    "timeout_error": "Request timeout - processing took too long",
    "rate_limit_exceeded": "Rate limit exceeded - please try again later",
    "service_unavailable": "Service temporarily unavailable"
}

# Logging Configuration
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

def get_environment() -> str:
    """Get current environment (development, staging, production)"""
    return os.getenv("ENVIRONMENT", "development").lower()

def is_production() -> bool:
    """Check if running in production environment"""
    return get_environment() == "production"

def is_development() -> bool:
    """Check if running in development environment"""
    return get_environment() == "development"

def get_debug_mode() -> bool:
    """Get debug mode setting"""
    return os.getenv("DEBUG", "false").lower() == "true" and not is_production()
