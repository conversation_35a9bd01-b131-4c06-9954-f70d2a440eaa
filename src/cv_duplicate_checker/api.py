"""
FastAPI application for CV Duplicate Checker Agent
"""

import json
import logging
import os
from contextlib import asynccontextmanager
from typing import Any, Dict

from fastapi import <PERSON><PERSON><PERSON>, Form, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from .agent import CVDuplicateCheckerAgent
from .api_models import (
    ActionDetail,
    ActionSummary,
    CVCheckRequest,
    CVCheckResponse,
    CVData,
    ErrorResponse,
    HealthResponse,
    SectionSummary,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Agent instance with lifecycle management
agent: CVDuplicateCheckerAgent = None
agent_request_count = 0
AGENT_REFRESH_THRESHOLD = 1000  # Refresh agent every 1000 requests to prevent memory accumulation

# Setup templates and static files
templates = Jinja2Templates(directory="templates")


async def cleanup_agent():
    """Clean up current agent instance to prevent memory leaks"""
    global agent
    if agent:
        # Clear any large objects the agent might be holding
        if hasattr(agent, "llm"):
            # Clear LLM client cache if exists
            del agent.llm
        if hasattr(agent, "graph"):
            # Clear graph state
            del agent.graph
        del agent
        agent = None
        # Force garbage collection
        import gc

        gc.collect()
        logger.info("Agent cleaned up successfully")


async def get_or_refresh_agent() -> CVDuplicateCheckerAgent:
    """Get agent instance, refreshing if needed to prevent memory accumulation"""
    global agent, agent_request_count

    agent_request_count += 1

    # Refresh agent if threshold reached
    if agent_request_count >= AGENT_REFRESH_THRESHOLD:
        logger.info(f"Refreshing agent after {agent_request_count} requests")
        await cleanup_agent()
        agent_request_count = 0

    # Initialize agent if needed
    if agent is None:
        try:
            agent = CVDuplicateCheckerAgent()
            logger.info("CV Duplicate Checker Agent initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize agent: {str(e)}")
            raise HTTPException(status_code=500, detail="Agent initialization failed")

    return agent


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    global agent

    # Startup
    logger.info("Starting CV Duplicate Checker API...")
    try:
        agent = CVDuplicateCheckerAgent()
        logger.info("CV Duplicate Checker Agent initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize agent: {str(e)}")
        agent = None

    yield

    # Shutdown
    logger.info("Shutting down CV Duplicate Checker API...")
    await cleanup_agent()


# Create FastAPI application
app = FastAPI(
    title="CV Duplicate Checker API",
    description="""
    An intelligent CV duplicate detection and merging system powered by LangGraph and OpenAI's LLM for semantic understanding.
    
    ## Features
    
    - **Semantic Matching**: Uses LLM to understand that "DHBK" equals "Đại học Bách Khoa"
    - **Parallel Processing**: All CV sections processed simultaneously using LangGraph
    - **Multi-language Support**: Handles Vietnamese ↔ English translations seamlessly
    - **Smart Actions**: ADD new info, UPDATE missing fields, or SKIP complete duplicates
    - **Reliable**: Graceful fallbacks if LLM unavailable
    - **Comprehensive**: Handles 10 CV sections (education, experience, certifications, etc.)
    
    ## Usage
    
    1. **POST /check-duplicates**: Process CV data and detect duplicates
    2. **GET /health**: Check API health status
    3. **GET /docs**: View interactive API documentation
    """,
    version="1.0.0",
    contact={
        "name": "CV Duplicate Checker API",
        "url": "https://github.com/your-repo/upzi-cv-duplicate-checker",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan,
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure based on your needs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def parse_agent_result(result_json: dict) -> CVCheckResponse:
    """Parse agent result into API response model"""
    try:
        # Parse processed CV
        processed_cv = CVData(**result_json["processed_cv"])

        # Parse action summary
        action_summary = ActionSummary(**result_json["action_summary"])

        # Parse section summary
        section_summary = {}
        for section, summary in result_json["section_summary"].items():
            section_summary[section] = SectionSummary(**summary)

        # Parse detailed actions
        detailed_actions = []
        for action in result_json["detailed_actions"]:
            detailed_actions.append(ActionDetail(**action))

        return CVCheckResponse(
            processed_cv=processed_cv,
            action_summary=action_summary,
            section_summary=section_summary,
            detailed_actions=detailed_actions,
        )
    except Exception as e:
        logger.error(f"Failed to parse agent result: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to parse processing result: {str(e)}"
        )


@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check():
    """
    Check the health status of the API and agent
    """
    try:
        # Try to get agent to ensure it's available
        test_agent = await get_or_refresh_agent()
        agent_ready = test_agent is not None
    except Exception:
        agent_ready = False

    return HealthResponse(status="healthy", version="1.0.0", agent_ready=agent_ready)


@app.post(
    "/check-duplicates",
    response_model=CVCheckResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid input data"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    tags=["CV Processing"],
)
async def check_cv_duplicates(request: CVCheckRequest):
    """
    Process CV data to detect and merge duplicates using semantic matching

    This endpoint:
    1. Compares existing CV data with new data
    2. Uses LLM-powered semantic matching to understand equivalences
    3. Returns merged CV with detailed action reports

    **Semantic Matching Examples:**
    - "DHBK" ↔ "Đại học Bách Khoa"
    - "Google" ↔ "Google LLC"
    - "MIT" ↔ "Massachusetts Institute of Technology"

    **Action Types:**
    - **ADD**: Completely new information
    - **UPDATE**: Existing entry with missing fields
    - **SKIP**: Complete duplicate found
    """
    try:
        # Get or refresh agent instance
        current_agent = await get_or_refresh_agent()

        # Convert request data to JSON strings for agent processing
        existing_cv_json = json.dumps(request.existing_cv.model_dump(), ensure_ascii=False)
        new_data_json = json.dumps(request.new_data.model_dump(), ensure_ascii=False)

        logger.info("Processing CV duplicate check request...")

        # Process with agent
        result = await current_agent.process(existing_cv_json, new_data_json)
        # Parse and return response
        response = parse_agent_result(result)

        logger.info(f"CV processing completed. Actions: {response.action_summary.total_actions}")
        return response

    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid input data: {str(e)}")
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to process CV data: {str(e)}"
        )


@app.get("/", tags=["Root"])
async def root():
    """
    API root endpoint with basic information
    """
    return {
        "message": "CV Duplicate Checker API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "web_interface": "/web",
        "endpoints": {
            "check_duplicates": "/check-duplicates",
            "health": "/health",
            "web_interface": "/web",
            "web_health": "/web/health",
        },
    }


# Web Interface Routes


@app.get("/web", response_class=HTMLResponse, tags=["Web Interface"], operation_id="web_interface_get")
@app.post("/web", response_class=HTMLResponse, tags=["Web Interface"], operation_id="web_interface_post")
async def web_interface(request: Request, existing_cv: str = Form(None), new_data: str = Form(None)):
    """
    Main web interface for testing the CV duplicate checker
    Handles both form display (GET) and processing (POST)
    """
    # Handle GET request - show form
    if request.method == "GET":
        return templates.TemplateResponse("index.html", {"request": request})

    # Handle POST request - process form data
    if not existing_cv or not new_data:
        return templates.TemplateResponse(
            "index.html", {"request": request, "messages": [("error", "Both existing CV and new data are required")]}
        )

    try:
        # Get or refresh agent instance
        current_agent = await get_or_refresh_agent()

        # Validate JSON inputs
        json.loads(existing_cv)  # Validate existing_cv JSON
        json.loads(new_data)  # Validate new_data JSON

        # Process with agent
        result = await current_agent.process(existing_cv, new_data)
        # Parse result
        response = parse_agent_result(result)

        # Create enhanced actions with display names for template rendering
        enhanced_actions = []
        for action in response.detailed_actions:
            display_fields = [
                "school",
                "company",
                "title",
                "certification_name",
                "language_name",
                "name",
                "hobby",
                "role",
            ]
            display_name = ""
            for field in display_fields:
                if field in action.item and action.item[field]:
                    display_name = action.item[field]
                    break

            # Create a dict with all action data plus display_name
            enhanced_action = action.model_dump()
            enhanced_action["display_name"] = display_name
            enhanced_actions.append(enhanced_action)

        # Format processed CV for display
        processed_cv_json = json.dumps(response.processed_cv.model_dump(), indent=2, ensure_ascii=False)
        raw_response = json.dumps(
            {
                "processed_cv": response.processed_cv.model_dump(),
                "action_summary": response.action_summary.model_dump(),
                "section_summary": {k: v.model_dump() for k, v in response.section_summary.items()},
                "detailed_actions": [action.model_dump() for action in response.detailed_actions],
            },
            indent=2,
            ensure_ascii=False,
        )

        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
                "result": response,
                "enhanced_actions": enhanced_actions,
                "processed_cv_json": processed_cv_json,
                "raw_response": raw_response,
                "existing_cv": existing_cv,
                "new_data": new_data,
                "show_results": True,
                "result_action_summary": response.action_summary.model_dump(),
                "result_section_summary": {k: v.model_dump() for k, v in response.section_summary.items()},
                "result_processed_cv": response.processed_cv.model_dump(),
            },
        )

    except json.JSONDecodeError as e:
        return templates.TemplateResponse(
            "index.html", {"request": request, "messages": [("error", f"Invalid JSON format: {str(e)}")]}
        )
    except Exception as e:
        logger.error(f"Web processing error: {str(e)}")
        return templates.TemplateResponse(
            "index.html", {"request": request, "messages": [("error", f"Processing error: {str(e)}")]}
        )


@app.get("/web/health", response_class=HTMLResponse, tags=["Web Interface"])
async def web_health(request: Request):
    """
    Web interface health status page
    """
    try:
        # Get health data from API endpoint
        health_data = await health_check()

        return templates.TemplateResponse("health.html", {"request": request, "health_data": health_data.model_dump()})
    except Exception as e:
        logger.error(f"Web health check error: {str(e)}")
        return templates.TemplateResponse(
            "health.html",
            {
                "request": request,
                "health_data": {"status": "error", "version": "1.0.0", "agent_ready": False},
                "error": str(e),
            },
        )


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": "HTTPException", "message": exc.detail, "status_code": exc.status_code},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "InternalServerError",
            "message": "An unexpected error occurred",
            "details": str(exc) if os.getenv("DEBUG") else None,
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Load environment variables
    from dotenv import load_dotenv

    load_dotenv()

    # Run the API
    uvicorn.run("cv_duplicate_checker.api:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
