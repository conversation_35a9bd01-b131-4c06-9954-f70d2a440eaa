"""CV Duplicate Checker Agent using LangGraph and OpenAI LLM"""

import json
import logging
from typing import Any, Dict, List

from dotenv import load_dotenv
from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, START, StateGraph

from .models import AgentState

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CVDuplicateCheckerAgent:
    """CV Duplicate Checker Agent with semantic matching capabilities"""

    def __init__(self, model_name: str = "gpt-4o-mini", temperature: float = 0):
        """Initialize the agent with LLM and workflow graph"""
        self.llm = ChatOpenAI(model=model_name, temperature=temperature)
        self.graph = self._create_graph()

    def _create_graph(self) -> StateGraph:
        """Create LangGraph workflow with parallel processing"""
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("parse_input", self._parse_input)
        workflow.add_node("check_education", self._check_education)
        workflow.add_node("check_experience", self._check_experience)
        workflow.add_node("check_extra_curricular", self._check_extra_curricular)
        workflow.add_node("check_certification", self._check_certification)
        workflow.add_node("check_language", self._check_language)
        workflow.add_node("check_achievement", self._check_achievement)
        workflow.add_node("check_reference", self._check_reference)
        workflow.add_node("check_external_doc", self._check_external_doc)
        workflow.add_node("check_summary", self._check_summary)
        workflow.add_node("check_hobby", self._check_hobby)
        workflow.add_node("merge_results", self._merge_results)

        # Define parallel workflow
        workflow.add_edge(START, "parse_input")

        # From parse_input, all sections run in parallel
        workflow.add_edge("parse_input", "check_education")
        workflow.add_edge("parse_input", "check_experience")
        workflow.add_edge("parse_input", "check_extra_curricular")
        workflow.add_edge("parse_input", "check_certification")
        workflow.add_edge("parse_input", "check_language")
        workflow.add_edge("parse_input", "check_achievement")
        workflow.add_edge("parse_input", "check_reference")
        workflow.add_edge("parse_input", "check_external_doc")
        workflow.add_edge("parse_input", "check_summary")
        workflow.add_edge("parse_input", "check_hobby")

        # All sections lead to merge_results
        workflow.add_edge("check_education", "merge_results")
        workflow.add_edge("check_experience", "merge_results")
        workflow.add_edge("check_extra_curricular", "merge_results")
        workflow.add_edge("check_certification", "merge_results")
        workflow.add_edge("check_language", "merge_results")
        workflow.add_edge("check_achievement", "merge_results")
        workflow.add_edge("check_reference", "merge_results")
        workflow.add_edge("check_external_doc", "merge_results")
        workflow.add_edge("check_summary", "merge_results")
        workflow.add_edge("check_hobby", "merge_results")

        # merge_results to END
        workflow.add_edge("merge_results", END)

        return workflow.compile()

    def _parse_input(self, state: AgentState) -> Dict[str, Any]:
        """Parse input and initialize processed_cv"""
        logger.info("Parsing input data...")

        return {
            "processed_cv": state["existing_cv"].copy(),
            "analysis_results": [],
            # Initialize section results
            "education_result": None,
            "experience_result": None,
            "extra_curricular_result": None,
            "certification_result": None,
            "language_result": None,
            "achievement_result": None,
            "reference_result": None,
            "external_doc_result": None,
            "summary_result": None,
            "hobby_result": None,
        }

    async def _is_semantically_similar(self, value1: str, value2: str, field_type: str = "general") -> bool:
        """
        Use LLM to check semantic similarity

        Args:
            value1: First value
            value2: Second value
            field_type: Field type (school, company, certification, etc.)

        Returns:
            True if values are semantically similar
        """
        if not value1 or not value2:
            return False

        # If exactly the same, no need for LLM
        if value1.strip().lower() == value2.strip().lower():
            return True

        try:
            system_prompt = f"""
You are a semantic analysis expert. Your task is to determine if two values are semantically equivalent.

Cases to consider as equivalent:
- Abbreviations and full names (e.g., DHBK and Đại học Bách Khoa)
- Variations of the same name (e.g., MIT and Massachusetts Institute of Technology)
- Different spellings of the same organization/school/company
- Equivalent technical terms (e.g., Computer Science and Khoa học máy tính)

Current field type: {field_type}

Only respond with 'true' or 'false'.
"""

            human_prompt = f"Value 1: {value1}\nValue 2: {value2}\n\nAre these two values semantically equivalent?"

            messages = [SystemMessage(content=system_prompt), HumanMessage(content=human_prompt)]

            response = await self.llm.ainvoke(messages)
            result = response.content.strip().lower()

            return result == "true"

        except Exception as e:
            logger.warning(f"LLM semantic comparison failed: {e}. Falling back to exact match.")
            return value1.strip().lower() == value2.strip().lower()

    async def _check_section_duplicates(
        self,
        section_name: str,
        new_items: List[Dict],
        existing_items: List[Dict],
        duplicate_criteria: List[str],
        updatable_fields: List[str],
    ) -> List[Dict]:
        """
        Check duplicates for a specific section with semantic matching support

        Args:
            section_name: Section name (education, experience, etc.)
            new_items: New items to check
            existing_items: Existing items
            duplicate_criteria: Criteria to determine complete duplicates
            updatable_fields: Fields that can be updated

        Returns:
            List of actions to perform
        """
        if not new_items:
            return []

        # Limit actions list size to prevent unbounded growth
        MAX_ACTIONS_PER_SECTION = 100
        actions = []

        def safe_append_action(action_data):
            """Safely append action with size limit to prevent memory accumulation"""
            if len(actions) < MAX_ACTIONS_PER_SECTION:
                actions.append(action_data)
            else:
                logger.warning(
                    f"Actions list limit reached for section {section_name}. Skipping additional actions to prevent memory accumulation."
                )

        for new_item in new_items:
            duplicate_found = False

            for idx, existing_item in enumerate(existing_items):
                # Check duplicates with semantic matching
                is_duplicate = True

                for field in duplicate_criteria:
                    new_value = new_item.get(field)
                    existing_value = existing_item.get(field)

                    if new_value is None or existing_value is None:
                        if new_value != existing_value:
                            is_duplicate = False
                            break
                        continue

                    # Use semantic matching for text fields
                    if isinstance(new_value, str) and isinstance(existing_value, str):
                        # Determine field type to improve semantic matching
                        field_type = "general"
                        if field in ["school", "university"]:
                            field_type = "school"
                        elif field in ["company", "organization"]:
                            field_type = "company"
                        elif field in ["major", "field_of_study"]:
                            field_type = "major"
                        elif field in ["title", "position", "role"]:
                            field_type = "position"
                        elif field in ["certification_name"]:
                            field_type = "certification"

                        if not await self._is_semantically_similar(new_value, existing_value, field_type):
                            is_duplicate = False
                            break
                    else:
                        # For non-text fields, use exact comparison
                        if new_value != existing_value:
                            is_duplicate = False
                            break

                if is_duplicate:
                    duplicate_found = True

                    # Check if any fields need to be supplemented
                    fields_to_update = []
                    for field in updatable_fields:
                        if new_item.get(field) is not None and existing_item.get(field) is None:
                            fields_to_update.append(field)

                    if fields_to_update:
                        # Update missing information
                        safe_append_action({
                            "action": "update",
                            "item": new_item,
                            "existing_index": idx,
                            "fields_to_update": fields_to_update,
                            "reason": f"Bổ sung thông tin thiếu: {', '.join(fields_to_update)} (phát hiện bằng semantic matching)",
                            "section": section_name,
                        })
                    else:
                        # Complete duplicate, skip
                        safe_append_action({
                            "action": "skip",
                            "item": new_item,
                            "reason": "Trùng lặp hoàn toàn (phát hiện bằng semantic matching)",
                            "section": section_name,
                        })
                    break

            if not duplicate_found:
                # Add new
                safe_append_action({
                    "action": "add",
                    "item": new_item,
                    "reason": "Thông tin mới",
                    "section": section_name,
                })

        return actions

    async def _check_education(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Education"""
        logger.info("Checking education duplicates...")

        new_education = state["new_data"].get("education", [])
        existing_education = state["processed_cv"].get("education", []).copy()

        actions = await self._check_section_duplicates(
            "education",
            new_education,
            existing_education,
            duplicate_criteria=["school", "major"],
            updatable_fields=["start_date", "end_date", "degree", "description", "favorite_subjects", "gpa"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_education.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_education[idx][field] = action["item"][field]

        return {"education_result": {"data": existing_education, "actions": actions}}

    async def _check_experience(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Experience"""
        logger.info("Checking experience duplicates...")

        new_experience = state["new_data"].get("experience", [])
        existing_experience = state["processed_cv"].get("experience", []).copy()

        actions = await self._check_section_duplicates(
            "experience",
            new_experience,
            existing_experience,
            duplicate_criteria=["title", "company", "start_date"],
            updatable_fields=["description", "end_date", "skills"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_experience.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_experience[idx][field] = action["item"][field]

        return {"experience_result": {"data": existing_experience, "actions": actions}}

    async def _check_extra_curricular(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Extra-curricular"""
        logger.info("Checking extra-curricular duplicates...")

        new_extra = state["new_data"].get("extra_curricular", [])
        existing_extra = state["processed_cv"].get("extra_curricular", []).copy()

        actions = await self._check_section_duplicates(
            "extra_curricular",
            new_extra,
            existing_extra,
            duplicate_criteria=["role", "organization", "start_date"],
            updatable_fields=["end_date", "description", "skills"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_extra.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_extra[idx][field] = action["item"][field]

        return {"extra_curricular_result": {"data": existing_extra, "actions": actions}}

    async def _check_certification(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Certification"""
        logger.info("Checking certification duplicates...")

        new_cert = state["new_data"].get("certification", [])
        existing_cert = state["processed_cv"].get("certification", []).copy()

        actions = await self._check_section_duplicates(
            "certification",
            new_cert,
            existing_cert,
            duplicate_criteria=["certification_name", "organization"],
            updatable_fields=["issue_date", "expire_date", "url"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_cert.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_cert[idx][field] = action["item"][field]

        return {"certification_result": {"data": existing_cert, "actions": actions}}

    async def _check_language(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Language"""
        logger.info("Checking language duplicates...")

        new_lang = state["new_data"].get("language", [])
        existing_lang = state["processed_cv"].get("language", []).copy()

        actions = await self._check_section_duplicates(
            "language", new_lang, existing_lang, duplicate_criteria=["language_name"], updatable_fields=["level"]
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_lang.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_lang[idx][field] = action["item"][field]

        return {"language_result": {"data": existing_lang, "actions": actions}}

    async def _check_achievement(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Achievement"""
        logger.info("Checking achievement duplicates...")

        new_achieve = state["new_data"].get("achievement", [])
        existing_achieve = state["processed_cv"].get("achievement", []).copy()

        actions = await self._check_section_duplicates(
            "achievement",
            new_achieve,
            existing_achieve,
            duplicate_criteria=["title", "organization", "issue_date"],
            updatable_fields=["description"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_achieve.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_achieve[idx][field] = action["item"][field]

        return {"achievement_result": {"data": existing_achieve, "actions": actions}}

    async def _check_reference(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Reference"""
        logger.info("Checking reference duplicates...")

        new_ref = state["new_data"].get("reference", [])
        existing_ref = state["processed_cv"].get("reference", []).copy()

        actions = await self._check_section_duplicates(
            "reference",
            new_ref,
            existing_ref,
            duplicate_criteria=["email"],
            updatable_fields=["name", "position", "company"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_ref.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_ref[idx][field] = action["item"][field]

        return {"reference_result": {"data": existing_ref, "actions": actions}}

    async def _check_external_doc(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for External Doc"""
        logger.info("Checking external doc duplicates...")

        new_doc = state["new_data"].get("external_doc", [])
        existing_doc = state["processed_cv"].get("external_doc", []).copy()

        actions = await self._check_section_duplicates(
            "external_doc",
            new_doc,
            existing_doc,
            duplicate_criteria=["link"],
            updatable_fields=["title", "description"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_doc.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_doc[idx][field] = action["item"][field]

        return {"external_doc_result": {"data": existing_doc, "actions": actions}}

    async def _check_summary(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Summary"""
        logger.info("Checking summary duplicates...")

        new_summary = state["new_data"].get("summary", [])
        existing_summary = state["processed_cv"].get("summary", []).copy()

        actions = await self._check_section_duplicates(
            "summary", new_summary, existing_summary, duplicate_criteria=["summary"], updatable_fields=[]
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_summary.append(action["item"])

        return {"summary_result": {"data": existing_summary, "actions": actions}}

    async def _check_hobby(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Hobby"""
        logger.info("Checking hobby duplicates...")

        new_hobby = state["new_data"].get("hobby", [])
        existing_hobby = state["processed_cv"].get("hobby", []).copy()

        actions = await self._check_section_duplicates(
            "hobby", new_hobby, existing_hobby, duplicate_criteria=["hobby"], updatable_fields=["description"]
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_hobby.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_hobby[idx][field] = action["item"][field]

        return {"hobby_result": {"data": existing_hobby, "actions": actions}}

    def _merge_results(self, state: AgentState) -> Dict[str, Any]:
        """Merge final results from section results"""
        logger.info("Merging final results...")

        # Collect all section results
        section_results = {
            "education": state.get("education_result"),
            "experience": state.get("experience_result"),
            "extra_curricular": state.get("extra_curricular_result"),
            "certification": state.get("certification_result"),
            "language": state.get("language_result"),
            "achievement": state.get("achievement_result"),
            "reference": state.get("reference_result"),
            "external_doc": state.get("external_doc_result"),
            "summary": state.get("summary_result"),
            "hobby": state.get("hobby_result"),
        }

        # Build processed_cv from section results
        processed_cv = state["processed_cv"].copy()
        all_actions = []
        MAX_TOTAL_ACTIONS = 1000  # Limit total actions to prevent memory accumulation

        for section_name, result in section_results.items():
            if result is not None:
                # Update processed_cv with data from section result
                processed_cv[section_name] = result["data"]

                # Collect actions with size limit
                section_actions = result.get("actions", [])
                if len(all_actions) + len(section_actions) <= MAX_TOTAL_ACTIONS:
                    all_actions.extend(section_actions)
                else:
                    # Add only what fits within the limit
                    remaining_slots = MAX_TOTAL_ACTIONS - len(all_actions)
                    if remaining_slots > 0:
                        all_actions.extend(section_actions[:remaining_slots])
                        logger.warning(f"Total actions limit reached. Truncated actions from section {section_name}")
                    break

        # Create action summary
        action_summary = {
            "total_actions": len(all_actions),
            "added": len([a for a in all_actions if a["action"] == "add"]),
            "updated": len([a for a in all_actions if a["action"] == "update"]),
            "skipped": len([a for a in all_actions if a["action"] == "skip"]),
        }

        # Group actions by section
        section_summary = {}
        action_mapping = {"add": "added", "update": "updated", "skip": "skipped"}

        for action in all_actions:
            section = action.get("section", "unknown")
            if section not in section_summary:
                section_summary[section] = {"added": 0, "updated": 0, "skipped": 0}

            action_type = action_mapping.get(action["action"], action["action"])
            if action_type in section_summary[section]:
                section_summary[section][action_type] += 1

        return {
            "final_result": {
                "processed_cv": processed_cv,
                "action_summary": action_summary,
                "section_summary": section_summary,
                "detailed_actions": all_actions,
            }
        }

    async def process(self, existing_cv_json: str, new_data_json: str) -> str:
        """
        Process CV duplicate checking

        Args:
            existing_cv_json: JSON string of existing CV
            new_data_json: JSON string of new data

        Returns:
            JSON string of processed result
        """
        try:
            # Parse JSON input
            existing_cv = json.loads(existing_cv_json)
            new_data = json.loads(new_data_json)

            # Create initial state
            initial_state = AgentState(
                existing_cv=existing_cv,
                new_data=new_data,
                processed_cv={},
                current_section=None,
                analysis_results=[],
                final_result=None,
                education_result=None,
                experience_result=None,
                extra_curricular_result=None,
                certification_result=None,
                language_result=None,
                achievement_result=None,
                reference_result=None,
                external_doc_result=None,
                summary_result=None,
                hobby_result=None,
            )

            # Run graph
            final_state = await self.graph.ainvoke(initial_state)

            # Extract result before cleanup
            result_data = final_state["final_result"]

            # Return result as JSON
            return result_data

        except Exception as e:
            import traceback

            logger.error(f"Error processing CV: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            return json.dumps({"error": str(e), "traceback": traceback.format_exc()}, ensure_ascii=False)
