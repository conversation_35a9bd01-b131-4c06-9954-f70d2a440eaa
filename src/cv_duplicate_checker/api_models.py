"""
API Models for CV Duplicate Checker FastAPI endpoints
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class CVData(BaseModel):
    """CV data structure for API requests"""
    education: Optional[List[Dict[str, Any]]] = Field(default=None, description="Education entries")
    experience: Optional[List[Dict[str, Any]]] = Field(default=None, description="Work experience entries")
    extra_curricular: Optional[List[Dict[str, Any]]] = Field(default=None, description="Extra-curricular activities")
    certification: Optional[List[Dict[str, Any]]] = Field(default=None, description="Certifications")
    language: Optional[List[Dict[str, Any]]] = Field(default=None, description="Language skills")
    achievement: Optional[List[Dict[str, Any]]] = Field(default=None, description="Achievements and awards")
    reference: Optional[List[Dict[str, Any]]] = Field(default=None, description="References")
    external_doc: Optional[List[Dict[str, Any]]] = Field(default=None, description="External documents/links")
    summary: Optional[List[Dict[str, Any]]] = Field(default=None, description="Summary sections")
    hobby: Optional[List[Dict[str, Any]]] = Field(default=None, description="Hobbies and interests")


class CVCheckRequest(BaseModel):
    """Request model for CV duplicate checking endpoint"""
    existing_cv: CVData = Field(description="Existing CV data to compare against")
    new_data: CVData = Field(description="New CV data to be processed")

    class Config:
        json_schema_extra = {
            "example": {
                "existing_cv": {
                    "education": [
                        {
                            "school": "DHBK",
                            "major": "Computer Science",
                            "start_date": "2020"
                        }
                    ],
                    "experience": [
                        {
                            "title": "Software Engineer",
                            "company": "Google",
                            "start_date": "2022-01-01"
                        }
                    ]
                },
                "new_data": {
                    "education": [
                        {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8
                        }
                    ],
                    "experience": [
                        {
                            "title": "Software Engineer",
                            "company": "Google LLC",
                            "start_date": "2022-01-01",
                            "end_date": "2023-12-31",
                            "skills": ["Python", "Go"]
                        }
                    ]
                }
            }
        }


class ActionDetail(BaseModel):
    """Detailed information about a specific action taken"""
    action: str = Field(description="Type of action: add, update, or skip")
    item: Dict[str, Any] = Field(description="The processed item")
    existing_index: Optional[int] = Field(default=None, description="Index in existing CV (for updates)")
    fields_to_update: Optional[List[str]] = Field(default=None, description="Fields that were updated")
    reason: str = Field(description="Reason for this action")
    section: str = Field(description="CV section name")


class ActionSummary(BaseModel):
    """Summary of all actions taken"""
    total_actions: int = Field(description="Total number of actions performed")
    added: int = Field(description="Number of new items added")
    updated: int = Field(description="Number of existing items updated")
    skipped: int = Field(description="Number of duplicate items skipped")


class SectionSummary(BaseModel):
    """Summary for a specific CV section"""
    added: int = Field(description="Items added in this section")
    updated: int = Field(description="Items updated in this section")
    skipped: int = Field(description="Items skipped in this section")


class CVCheckResponse(BaseModel):
    """Response model for CV duplicate checking endpoint"""
    processed_cv: CVData = Field(description="The final processed CV with merged data")
    action_summary: ActionSummary = Field(description="Summary of all actions taken")
    section_summary: Dict[str, SectionSummary] = Field(description="Summary by CV section")
    detailed_actions: List[ActionDetail] = Field(description="Detailed list of all actions performed")

    class Config:
        json_schema_extra = {
            "example": {
                "processed_cv": {
                    "education": [
                        {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8
                        }
                    ]
                },
                "action_summary": {
                    "total_actions": 2,
                    "added": 0,
                    "updated": 1,
                    "skipped": 1
                },
                "section_summary": {
                    "education": {
                        "added": 0,
                        "updated": 1,
                        "skipped": 0
                    }
                },
                "detailed_actions": [
                    {
                        "action": "update",
                        "item": {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8
                        },
                        "existing_index": 0,
                        "fields_to_update": ["degree", "gpa"],
                        "reason": "Bổ sung thông tin thiếu: degree, gpa (phát hiện bằng semantic matching)",
                        "section": "education"
                    }
                ]
            }
        }


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(description="Service status")
    version: str = Field(description="API version")
    agent_ready: bool = Field(description="Whether the CV duplicate checker agent is ready")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "agent_ready": True
            }
        }


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(description="Error type")
    message: str = Field(description="Detailed error message")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid CV data provided",
                "details": {
                    "field": "education",
                    "issue": "Missing required field 'school'"
                }
            }
        }