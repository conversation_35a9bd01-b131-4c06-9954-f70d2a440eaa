# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CV Duplicate Checker is an intelligent system that detects and merges duplicate information in CV/resume data using semantic understanding powered by LLMs. It performs semantic matching (e.g., "DHBK" = "Đ<PERSON><PERSON> họ<PERSON>á<PERSON> Khoa"), handles multi-language content (Vietnamese ↔ English), and processes 10 CV sections in parallel using LangGraph state management.

## Core Architecture

The system uses **LangGraph** for workflow orchestration, processing all 10 CV sections simultaneously through a state graph. The main components:

- **Agent (`src/cv_duplicate_checker/agent.py`)**: LangGraph-based processing engine with `AgentState` TypedDict
- **API (`src/cv_duplicate_checker/api.py`)**: FastAPI REST API with web interface at `/web`
- **Models (`src/cv_duplicate_checker/models.py`)**: Pydantic models for 10 CV sections
- **API Models (`src/cv_duplicate_checker/api_models.py`)**: Request/response schemas

## Key Technical Details

**Semantic Matching**: Only applies to text fields using OpenAI GPT-4o-mini. Non-text fields use exact matching. Context-aware prompts vary by section (schools, companies, certifications).

**Duplicate Detection**: Each section has specific identifier fields:
- Education: school + major
- Experience: title + company + start_date  
- Certification: certification_name + organization
- Language: language_name
- Achievement: title + organization + issue_date

**Actions**: Agent decides to ADD (new), UPDATE (missing fields), or SKIP (complete duplicate) based on semantic analysis.

## Development Commands

**Setup:**
```bash
uv sync && source .venv/bin/activate
```

**Run API Server:**
```bash
python main.py --reload  # Saves config to .env
python main.py --config  # Show current config
```

**Testing:**
```bash
uv run python run_tests.py                    # All tests
uv run python run_tests.py --comprehensive    # Full test suite
uv run python run_tests.py --semantic         # Semantic matching tests
```

**Docker:**
```bash
docker-compose up -d      # Production
docker-compose -f docker-compose.dev.yml up -d  # Development
```

## Configuration

Uses `.env` file for persistent configuration. Command-line arguments auto-save to `.env`. Key settings: API host/port, OpenAI API key, log level.

## Important Notes

- Python 3.13+ required with full type hints
- Uses `uv` package manager for fast dependency resolution
- Async architecture throughout for optimal performance
- CORS enabled for cross-origin API requests
- Comprehensive error handling with appropriate HTTP status codes