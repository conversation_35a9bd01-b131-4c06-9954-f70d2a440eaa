# Docker Setup for CV Duplicate Checker

This guide shows how to run the CV Duplicate Checker using Docker and Docker Compose.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed
- [Docker Compose](https://docs.docker.com/compose/install/) installed
- OpenAI API key

## Quick Start

### 1. Set up environment variables

```bash
# Copy the example environment file
cp .env.docker.example .env

# Edit .env and add your OpenAI API key
nano .env  # or use your preferred editor
```

### 2. Run the application

**Production mode:**
```bash
docker-compose up -d
```

**Development mode (with live reload):**
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### 3. Access the application

- **Web Interface**: http://localhost:8000/web
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `OPENAI_API_KEY` | Your OpenAI API key | - | ✅ |
| `OPENAI_MODEL` | OpenAI model to use | `gpt-4o-mini` | ❌ |
| `OPENAI_TEMPERATURE` | LLM temperature (0-1) | `0` | ❌ |
| `API_PORT` | Port to expose on host | `8000` | ❌ |
| `API_LOG_LEVEL` | Log level | `info` | ❌ |
| `DEBUG` | Enable debug mode | `false` | ❌ |

## Docker Commands

### Basic Operations

```bash
# Start services in background
docker-compose up -d

# Start with live logs
docker-compose up

# Stop services
docker-compose down

# Rebuild and start
docker-compose up --build

# View logs
docker-compose logs -f cv-duplicate-checker
```

### Development Commands

```bash
# Development mode with live reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Run specific profile
docker-compose --profile dev up

# Shell into container
docker-compose exec cv-duplicate-checker bash

# View container status
docker-compose ps
```

### Maintenance Commands

```bash
# Remove all containers and volumes
docker-compose down -v

# Remove images
docker-compose down --rmi all

# Clean up system
docker system prune -a
```

## Docker Configuration

### Dockerfile Features

- **Multi-stage build** for optimized image size
- **Non-root user** for security
- **Health checks** for container monitoring
- **uv package manager** for fast dependency installation
- **Layer caching** for faster rebuilds

### docker-compose.yml Features

- **Environment variable management**
- **Volume mounting** for development
- **Health checks** and restart policies
- **Network isolation**
- **Development profiles**

## Development Workflow

### Live Reload Development

1. Start development containers:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
   ```

2. Edit files in `src/`, `templates/`, or `static/`

3. Changes are automatically reflected (live reload enabled)

### Production Deployment

1. Set environment variables:
   ```bash
   export OPENAI_API_KEY="your-api-key"
   export API_PORT=8000
   ```

2. Start production containers:
   ```bash
   docker-compose up -d
   ```

3. Monitor health:
   ```bash
   curl http://localhost:8000/health
   ```

## Troubleshooting

### Common Issues

**Container won't start:**
```bash
# Check logs
docker-compose logs cv-duplicate-checker

# Check environment variables
docker-compose config
```

**Port already in use:**
```bash
# Change port in .env file
echo "API_PORT=8080" >> .env
docker-compose up
```

**OpenAI API errors:**
```bash
# Verify API key
docker-compose exec cv-duplicate-checker python -c "import os; print('API key set:', bool(os.getenv('OPENAI_API_KEY')))"
```

### Performance Optimization

**Faster builds:**
- Use `.dockerignore` to exclude unnecessary files
- Layer dependencies separately from source code
- Use multi-stage builds

**Resource limits:**
```yaml
services:
  cv-duplicate-checker:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

## Security Considerations

- ✅ Non-root user in container
- ✅ Environment variable management
- ✅ No API keys in images
- ✅ Network isolation
- ⚠️ Consider adding API authentication for production
- ⚠️ Use secrets management for production deployments

## Production Deployment

For production environments:

1. **Use environment-specific configurations**
2. **Set up proper logging and monitoring**
3. **Configure reverse proxy (nginx/traefik)**
4. **Set up SSL/TLS certificates**
5. **Use orchestration platforms (Docker Swarm/Kubernetes)**

Example production docker-compose.prod.yml:
```yaml
version: '3.8'
services:
  cv-duplicate-checker:
    environment:
      - API_LOG_LEVEL=warning
      - DEBUG=false
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
      restart_policy:
        condition: any
        max_attempts: 3
```