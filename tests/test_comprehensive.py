import asyncio
import json
import sys
import os

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from cv_duplicate_checker import CVDuplicateCheckerAgent


class TestCVDuplicateChecker:
    """Comprehensive test cases for CV Duplicate Checker Agent"""

    def setup_method(self):
        """Setup test agent for each test"""
        self.agent = CVDuplicateCheckerAgent()
    
    def _run_agent_process(self, existing_cv, new_data):
        """Wrapper to run async agent.process in sync context"""
        async def _async_process():
            return await self.agent.process(existing_cv, new_data)
        
        return asyncio.run(_async_process())

    def test_empty_cv_add_new_data(self):
        """Test adding data to empty CV"""
        existing_cv = {}
        new_data = {
            "education": [
                {
                    "school": "MIT",
                    "major": "Computer Science",
                    "start_date": "2020",
                    "end_date": "2024",
                    "degree": "Bachelor",
                    "gpa": 3.8
                }
            ],
            "experience": [
                {
                    "title": "Software Engineer Intern",
                    "company": "Google",
                    "start_date": "2023-06-01",
                    "end_date": "2023-08-31",
                    "description": "Developed ML models",
                    "skills": ["Python", "TensorFlow"]
                }
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 2
        assert result_dict["action_summary"]["added"] == 2
        assert result_dict["action_summary"]["updated"] == 0
        assert result_dict["action_summary"]["skipped"] == 0

    def test_exact_duplicate_skip(self):
        """Test skipping exact duplicates"""
        existing_cv = {
            "education": [
                {
                    "school": "Harvard",
                    "major": "Economics",
                    "start_date": "2019",
                    "end_date": "2023",
                    "degree": "Bachelor",
                    "gpa": 3.9
                }
            ],
            "certification": [
                {
                    "certification_name": "CFA Level 1",
                    "organization": "CFA Institute",
                    "issue_date": "2023-06-01",
                    "expire_date": "2026-06-01"
                }
            ]
        }
        
        # Same data - should be skipped
        new_data = {
            "education": [
                {
                    "school": "Harvard",
                    "major": "Economics",
                    "start_date": "2019",
                    "end_date": "2023",
                    "degree": "Bachelor",
                    "gpa": 3.9
                }
            ],
            "certification": [
                {
                    "certification_name": "CFA Level 1",
                    "organization": "CFA Institute",
                    "issue_date": "2023-06-01",
                    "expire_date": "2026-06-01"
                }
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 2
        assert result_dict["action_summary"]["added"] == 0
        assert result_dict["action_summary"]["updated"] == 0
        assert result_dict["action_summary"]["skipped"] == 2

    def test_partial_update_missing_fields(self):
        """Test updating items with missing fields"""
        existing_cv = {
            "language": [
                {"language_name": "English"},
                {"language_name": "Spanish"}
            ],
            "hobby": [
                {"hobby": "Photography"},
                {"hobby": "Hiking"}
            ]
        }
        
        new_data = {
            "language": [
                {"language_name": "English", "level": "Native"},
                {"language_name": "Spanish", "level": "Intermediate"},
                {"language_name": "French", "level": "Beginner"}
            ],
            "hobby": [
                {"hobby": "Photography", "description": "Landscape and portrait photography"},
                {"hobby": "Hiking", "description": "Mountain trails and national parks"},
                {"hobby": "Cooking", "description": "Italian and Asian cuisine"}
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 6
        assert result_dict["action_summary"]["added"] == 2  # French language, Cooking hobby
        assert result_dict["action_summary"]["updated"] == 4  # Updated levels and descriptions
        assert result_dict["action_summary"]["skipped"] == 0

    def test_complex_experience_matching(self):
        """Test complex matching with same company but different roles"""
        existing_cv = {
            "experience": [
                {
                    "title": "Junior Developer",
                    "company": "Tech Corp",
                    "start_date": "2020-01-01",
                    "end_date": "2021-12-31",
                    "description": "Backend development"
                },
                {
                    "title": "Senior Developer",
                    "company": "Tech Corp",
                    "start_date": "2022-01-01",
                    "description": "Full-stack development"
                }
            ]
        }
        
        new_data = {
            "experience": [
                # Update existing junior role
                {
                    "title": "Junior Developer",
                    "company": "Tech Corp",
                    "start_date": "2020-01-01",
                    "end_date": "2021-12-31",
                    "description": "Backend development",
                    "skills": ["Java", "Spring Boot"]
                },
                # Update existing senior role
                {
                    "title": "Senior Developer",
                    "company": "Tech Corp",
                    "start_date": "2022-01-01",
                    "end_date": "2023-06-30",
                    "description": "Full-stack development",
                    "skills": ["React", "Node.js", "AWS"]
                },
                # Add new role at same company
                {
                    "title": "Tech Lead",
                    "company": "Tech Corp",
                    "start_date": "2023-07-01",
                    "description": "Leading a team of 5 developers"
                }
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 3
        assert result_dict["action_summary"]["added"] == 1  # Tech Lead role
        assert result_dict["action_summary"]["updated"] == 2  # Updated both existing roles
        assert result_dict["action_summary"]["skipped"] == 0

    def test_all_sections_comprehensive(self):
        """Test all CV sections with mixed operations"""
        existing_cv = {
            "education": [
                {"school": "Stanford", "major": "AI", "start_date": "2018"}
            ],
            "experience": [
                {"title": "ML Engineer", "company": "AI Corp", "start_date": "2022-01-01"}
            ],
            "extra_curricular": [
                {"role": "President", "organization": "AI Club", "start_date": "2019-09-01"}
            ],
            "certification": [
                {"certification_name": "TensorFlow Developer", "organization": "Google"}
            ],
            "language": [
                {"language_name": "Python"},
                {"language_name": "English"}
            ],
            "achievement": [
                {"title": "Best Paper Award", "organization": "NeurIPS", "issue_date": "2022-12-01"}
            ],
            "reference": [
                {"email": "<EMAIL>"}
            ],
            "external_doc": [
                {"link": "https://github.com/user"}
            ],
            "summary": [
                {"summary": "AI researcher and engineer"}
            ],
            "hobby": [
                {"hobby": "Machine Learning Research"}
            ]
        }
        
        new_data = {
            "education": [
                # Update existing
                {"school": "Stanford", "major": "AI", "start_date": "2018", "end_date": "2022", "degree": "PhD", "gpa": 4.0},
                # Add new
                {"school": "Berkeley", "major": "CS", "start_date": "2014", "end_date": "2018", "degree": "Bachelor"}
            ],
            "experience": [
                # Update existing
                {"title": "ML Engineer", "company": "AI Corp", "start_date": "2022-01-01", "end_date": "2023-12-31", "skills": ["PyTorch", "JAX"]},
                # Add new
                {"title": "Senior ML Engineer", "company": "DeepMind", "start_date": "2024-01-01", "description": "LLM research"}
            ],
            "extra_curricular": [
                # Skip existing
                {"role": "President", "organization": "AI Club", "start_date": "2019-09-01"},
                # Add new
                {"role": "Mentor", "organization": "Google Summer of Code", "start_date": "2023-06-01", "end_date": "2023-08-31"}
            ],
            "certification": [
                # Update existing
                {"certification_name": "TensorFlow Developer", "organization": "Google", "issue_date": "2022-03-01", "expire_date": "2025-03-01"},
                # Add new
                {"certification_name": "AWS ML Specialty", "organization": "Amazon", "issue_date": "2023-06-01"}
            ],
            "language": [
                # Update existing
                {"language_name": "Python", "level": "Expert"},
                {"language_name": "English", "level": "Native"},
                # Add new
                {"language_name": "Mandarin", "level": "Intermediate"}
            ],
            "achievement": [
                # Skip existing
                {"title": "Best Paper Award", "organization": "NeurIPS", "issue_date": "2022-12-01"},
                # Add new
                {"title": "Outstanding Reviewer", "organization": "ICML", "issue_date": "2023-07-01", "description": "Top 10% reviewer"}
            ],
            "reference": [
                # Update existing
                {"email": "<EMAIL>", "name": "Dr. Andrew Ng", "position": "Professor", "company": "Stanford University"},
                # Add new
                {"email": "<EMAIL>", "name": "John Doe", "position": "Research Manager"}
            ],
            "external_doc": [
                # Update existing
                {"link": "https://github.com/user", "title": "GitHub Profile", "description": "Open source contributions"},
                # Add new
                {"link": "https://scholar.google.com/user", "title": "Google Scholar", "description": "Research publications"}
            ],
            "summary": [
                # Skip existing
                {"summary": "AI researcher and engineer"},
                # Add new
                {"summary": "Specializing in large language models and reinforcement learning"}
            ],
            "hobby": [
                # Update existing
                {"hobby": "Machine Learning Research", "description": "Exploring new architectures and algorithms"},
                # Add new
                {"hobby": "Teaching", "description": "Conducting workshops and tutorials"}
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        # Verify total actions
        assert result_dict["action_summary"]["total_actions"] == 21
        
        # Check section-wise summary
        assert result_dict["section_summary"]["education"]["added"] == 1
        assert result_dict["section_summary"]["education"]["updated"] == 1
        
        assert result_dict["section_summary"]["experience"]["added"] == 1
        assert result_dict["section_summary"]["experience"]["updated"] == 1
        
        assert result_dict["section_summary"]["extra_curricular"]["added"] == 1
        assert result_dict["section_summary"]["extra_curricular"]["skipped"] == 1
        assert result_dict["section_summary"]["extra_curricular"]["updated"] == 0
        
        assert result_dict["section_summary"]["certification"]["added"] == 1
        assert result_dict["section_summary"]["certification"]["updated"] == 1
        
        assert result_dict["section_summary"]["language"]["added"] == 1
        assert result_dict["section_summary"]["language"]["updated"] == 2
        
        assert result_dict["section_summary"]["achievement"]["added"] == 1
        assert result_dict["section_summary"]["achievement"]["skipped"] == 1
        assert result_dict["section_summary"]["achievement"]["updated"] == 0
        
        assert result_dict["section_summary"]["reference"]["added"] == 1
        assert result_dict["section_summary"]["reference"]["updated"] == 1
        
        assert result_dict["section_summary"]["external_doc"]["added"] == 1
        assert result_dict["section_summary"]["external_doc"]["updated"] == 1
        
        assert result_dict["section_summary"]["summary"]["added"] == 1
        assert result_dict["section_summary"]["summary"]["skipped"] == 1
        assert result_dict["section_summary"]["summary"]["updated"] == 0
        
        assert result_dict["section_summary"]["hobby"]["added"] == 1
        assert result_dict["section_summary"]["hobby"]["updated"] == 1

    def test_special_characters_and_unicode(self):
        """Test handling of special characters and unicode"""
        existing_cv = {
            "education": [
                {"school": "Université Paris-Saclay", "major": "Mathématiques", "start_date": "2020"}
            ],
            "certification": [
                {"certification_name": "日本語能力試験 N2", "organization": "国際交流基金"}
            ]
        }
        
        new_data = {
            "education": [
                {"school": "Université Paris-Saclay", "major": "Mathématiques", "start_date": "2020", "degree": "Maîtrise"},
                {"school": "北京大学", "major": "计算机科学", "start_date": "2018", "end_date": "2020"}
            ],
            "certification": [
                {"certification_name": "日本語能力試験 N2", "organization": "国際交流基金", "issue_date": "2023-07-01"},
                {"certification_name": "한국어능력시험 TOPIK II", "organization": "국립국제교육원", "issue_date": "2023-10-01"}
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv, ensure_ascii=False),
            json.dumps(new_data, ensure_ascii=False)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 4
        assert result_dict["action_summary"]["added"] == 2
        assert result_dict["action_summary"]["updated"] == 2

    def test_edge_cases(self):
        """Test edge cases and error handling"""
        # Test with None values
        existing_cv = {
            "education": [
                {"school": "MIT", "major": "CS", "start_date": None, "end_date": None}
            ]
        }
        
        new_data = {
            "education": [
                {"school": "MIT", "major": "CS", "start_date": "2020", "end_date": "2024"}
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        # Should update the None fields
        assert result_dict["action_summary"]["updated"] == 1
        assert "start_date" in result_dict["detailed_actions"][0]["fields_to_update"]
        assert "end_date" in result_dict["detailed_actions"][0]["fields_to_update"]

    def test_performance_large_dataset(self):
        """Test performance with large dataset"""
        # Create large CV with many entries
        existing_cv = {
            "experience": [
                {
                    "title": f"Role {i}",
                    "company": f"Company {i}",
                    "start_date": f"2020-{i:02d}-01",
                    "description": f"Description for role {i}"
                }
                for i in range(1, 51)  # 50 experiences
            ],
            "certification": [
                {
                    "certification_name": f"Cert {i}",
                    "organization": f"Org {i}"
                }
                for i in range(1, 31)  # 30 certifications
            ]
        }
        
        # Add some updates and new items
        new_data = {
            "experience": [
                # Update first 10
                {
                    "title": f"Role {i}",
                    "company": f"Company {i}",
                    "start_date": f"2020-{i:02d}-01",
                    "description": f"Description for role {i}",
                    "end_date": f"2023-{i:02d}-01",
                    "skills": ["Python", "Docker"]
                }
                for i in range(1, 11)
            ] + [
                # Add 5 new
                {
                    "title": f"New Role {i}",
                    "company": f"New Company {i}",
                    "start_date": f"2024-{i:02d}-01",
                    "description": f"New role description {i}"
                }
                for i in range(1, 6)
            ],
            "certification": [
                # Update first 5
                {
                    "certification_name": f"Cert {i}",
                    "organization": f"Org {i}",
                    "issue_date": f"2023-{i:02d}-01"
                }
                for i in range(1, 6)
            ] + [
                # Add 3 new
                {
                    "certification_name": f"New Cert {i}",
                    "organization": f"New Org {i}",
                    "issue_date": "2024-01-01"
                }
                for i in range(1, 4)
            ]
        }

        result = self._run_agent_process(
            json.dumps(existing_cv),
            json.dumps(new_data)
        )
        result_dict = json.loads(result)

        assert result_dict["action_summary"]["total_actions"] == 23
        assert result_dict["action_summary"]["added"] == 8  # 5 experiences + 3 certifications
        assert result_dict["action_summary"]["updated"] == 15  # 10 experiences + 5 certifications


def run_manual_tests():
    """Run tests manually without pytest"""
    test_instance = TestCVDuplicateChecker()
    
    tests = [
        ("Empty CV - Add New Data", test_instance.test_empty_cv_add_new_data),
        ("Exact Duplicate Skip", test_instance.test_exact_duplicate_skip),
        ("Partial Update Missing Fields", test_instance.test_partial_update_missing_fields),
        ("Complex Experience Matching", test_instance.test_complex_experience_matching),
        ("All Sections Comprehensive", test_instance.test_all_sections_comprehensive),
        ("Special Characters and Unicode", test_instance.test_special_characters_and_unicode),
        ("Edge Cases", test_instance.test_edge_cases),
        ("Performance Large Dataset", test_instance.test_performance_large_dataset),
    ]
    
    print("Running CV Duplicate Checker Tests...\n")
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            test_instance.setup_method()
            test_func()
            print(f"✅ {test_name} - PASSED")
            passed += 1
        except AssertionError as e:
            print(f"❌ {test_name} - FAILED: {str(e)}")
            print(f"   AssertionError details: {repr(e)}")
            failed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
            failed += 1
    
    print(f"\n{'='*50}")
    print(f"Total: {len(tests)} | Passed: {passed} | Failed: {failed}")
    print(f"{'='*50}")


if __name__ == "__main__":
    run_manual_tests()