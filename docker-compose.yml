version: '3.8'

services:
  cv-duplicate-checker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cv-duplicate-checker
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      # Required: OpenAI API key
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Optional: OpenAI configuration
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o-mini}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0}
      
      # API Server configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_LOG_LEVEL=${API_LOG_LEVEL:-info}
      - API_RELOAD=${API_RELOAD:-false}
      
      # Optional: Debug mode
      - DEBUG=${DEBUG:-false}
    volumes:
      # Development: Mount source code for live reload (uncomment for dev)
      # - ./src:/app/src:ro
      # - ./templates:/app/templates:ro
      # - ./static:/app/static:ro
      
      # Logs volume
      - cv-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - cv-network

  # Optional: Development override service
  cv-duplicate-checker-dev:
    extends: cv-duplicate-checker
    profiles: ["dev"]
    environment:
      - API_RELOAD=true
      - API_LOG_LEVEL=debug
      - DEBUG=true
    volumes:
      # Development: Live reload with source mounting
      - ./src:/app/src:ro
      - ./templates:/app/templates:ro
      - ./static:/app/static:ro
      - ./main.py:/app/main.py:ro
      - cv-logs:/app/logs
    command: ["python", "main.py", "--reload", "--log-level", "debug"]

volumes:
  cv-logs:
    driver: local

networks:
  cv-network:
    driver: bridge