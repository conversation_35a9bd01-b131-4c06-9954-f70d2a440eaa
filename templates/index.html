{% extends "base.html" %}

{% block title %}CV Duplicate Checker - Test Interface{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-gear"></i> Test Interface</h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadSampleData('basic')">
                    <i class="bi bi-file-text"></i> Basic Example
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadSampleData('semantic')">
                    <i class="bi bi-translate"></i> Semantic Example
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="loadSampleData('full')">
                    <i class="bi bi-collection"></i> Full Example
                </button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearForm()">
                    <i class="bi bi-trash"></i> Clear
                </button>
            </div>
        </div>
    </div>
</div>

<form id="cvTestForm" method="POST" action="{{ url_for('web_interface') }}">
    <div class="row">
        <!-- Existing CV Data -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-lines-fill"></i> Existing CV Data
                    </h5>
                    <small>Current CV information to compare against</small>
                </div>
                <div class="card-body">
                    <div class="position-relative">
                        <textarea 
                            id="existingCvData" 
                            name="existing_cv" 
                            class="form-control json-input" 
                            rows="20" 
                            placeholder="Enter existing CV JSON data here..."
                            required></textarea>
                        <div class="json-validation-indicator" id="existingCvIndicator"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i> 
                            JSON format with CV sections: education, experience, certifications, etc.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- New CV Data -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-person-plus"></i> New CV Data
                    </h5>
                    <small>New information to be processed and merged</small>
                </div>
                <div class="card-body">
                    <div class="position-relative">
                        <textarea 
                            id="newCvData" 
                            name="new_data" 
                            class="form-control json-input" 
                            rows="20" 
                            placeholder="Enter new CV JSON data here..."
                            required></textarea>
                        <div class="json-validation-indicator" id="newCvIndicator"></div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i> 
                            Same JSON structure as existing CV. System will detect duplicates semantically.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Section -->
    <div class="row">
        <div class="col-12 text-center">
            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                <i class="bi bi-play-circle"></i> 
                <span id="submitText">Check for Duplicates</span>
                <div class="spinner-border spinner-border-sm ms-2 d-none" id="submitSpinner"></div>
            </button>
        </div>
    </div>
</form>

<!-- Results Section -->
{% if show_results and result %}
<div class="row mt-5">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-clipboard-check"></i> Processing Results</h2>
            <button type="button" class="btn btn-outline-secondary" onclick="copyResults()">
                <i class="bi bi-clipboard"></i> Copy Results
            </button>
        </div>
    </div>
</div>

<!-- Action Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="bi bi-list-check display-4 text-primary"></i>
                <h5 class="card-title">Total Actions</h5>
                <h2 class="text-primary mb-0">{{ result.action_summary.total_actions }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="bi bi-plus-circle display-4 text-success"></i>
                <h5 class="card-title">Added</h5>
                <h2 class="text-success mb-0">{{ result.action_summary.added }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="bi bi-arrow-up-circle display-4 text-warning"></i>
                <h5 class="card-title">Updated</h5>
                <h2 class="text-warning mb-0">{{ result.action_summary.updated }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="bi bi-skip-forward-circle display-4 text-info"></i>
                <h5 class="card-title">Skipped</h5>
                <h2 class="text-info mb-0">{{ result.action_summary.skipped }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- Section Breakdown -->
{% if result.section_summary %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-bar-chart"></i> Section Breakdown</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for section, summary in result.section_summary.items() %}
                    {% if summary.added > 0 or summary.updated > 0 or summary.skipped > 0 %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="card border-light">
                            <div class="card-body p-3">
                                <h6 class="card-title text-capitalize">{{ section.replace('_', ' ') }}</h6>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-success d-block">{{ summary.added }}</small>
                                        <small class="text-muted">Added</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-warning d-block">{{ summary.updated }}</small>
                                        <small class="text-muted">Updated</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-info d-block">{{ summary.skipped }}</small>
                                        <small class="text-muted">Skipped</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Detailed Actions -->
{% if enhanced_actions %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list-ul"></i> Detailed Actions</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Section</th>
                                <th>Action</th>
                                <th>Item Details</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for action in enhanced_actions %}
                            <tr>
                                <td>
                                    <span class="badge bg-secondary">{{ action.section.replace('_', ' ').title() }}</span>
                                </td>
                                <td>
                                    {% if action.action == 'add' %}
                                        <span class="badge bg-success"><i class="bi bi-plus"></i> Add</span>
                                    {% elif action.action == 'update' %}
                                        <span class="badge bg-warning"><i class="bi bi-arrow-up"></i> Update</span>
                                    {% else %}
                                        <span class="badge bg-info"><i class="bi bi-skip-forward"></i> Skip</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="item-details">
                                        {% if action.display_name %}
                                            <strong>{{ action.display_name }}</strong>
                                        {% endif %}
                                        
                                        {% if action.fields_to_update %}
                                            <div class="mt-1">
                                                <small class="text-muted">Updated fields:</small>
                                                {% for field in action.fields_to_update %}
                                                    <span class="badge bg-light text-dark">{{ field }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">{{ action.reason }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Processed CV Output -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-file-person"></i> Final Processed CV</h5>
            </div>
            <div class="card-body">
                <div class="position-relative">
                    <pre id="processedCvJson" class="json-output"><code>{{ processed_cv_json }}</code></pre>
                    <button type="button" class="btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2" onclick="copyToClipboard('processedCvJson')">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Raw API Response (Collapsible) -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#rawResponse">
                        <i class="bi bi-code-slash"></i> Raw API Response
                        <i class="bi bi-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="rawResponse">
                <div class="card-body">
                    <div class="position-relative">
                        <pre id="rawApiResponse" class="json-output"><code>{{ raw_response }}</code></pre>
                        <button type="button" class="btn btn-sm btn-outline-secondary position-absolute top-0 end-0 m-2" onclick="copyToClipboard('rawApiResponse')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Help Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-question-circle"></i> How It Works</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="bi bi-search"></i> Semantic Matching</h6>
                        <p class="small text-muted">
                            Uses AI to understand that "DHBK" equals "Đại học Bách Khoa", 
                            "Google" equals "Google LLC", etc.
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-lightning"></i> Smart Actions</h6>
                        <ul class="small text-muted mb-0">
                            <li><strong>ADD:</strong> Completely new information</li>
                            <li><strong>UPDATE:</strong> Existing entry with missing fields</li>
                            <li><strong>SKIP:</strong> Complete duplicate found</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-collection"></i> CV Sections</h6>
                        <p class="small text-muted">
                            Supports education, experience, certifications, languages, 
                            achievements, references, and more.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sample Data Templates (Hidden) -->
<script type="application/json" id="sampleBasic">
{
  "existing_cv": {
    "education": [
      {
        "school": "DHBK",
        "major": "Computer Science",
        "start_date": "2020"
      }
    ],
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Google",
        "start_date": "2022-01-01"
      }
    ],
    "extra_curricular": [],
    "certification": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  },
  "new_data": {
    "education": [
      {
        "school": "Đại học Bách Khoa",
        "major": "Computer Science",
        "start_date": "2020",
        "degree": "Bachelor",
        "gpa": 3.8
      }
    ],
    "experience": [
      {
        "title": "Software Engineer",
        "company": "Google LLC",
        "start_date": "2022-01-01",
        "end_date": "2023-12-31",
        "skills": ["Python", "Go"]
      }
    ],
    "extra_curricular": [],
    "certification": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  }
}
</script>

<script type="application/json" id="sampleSemantic">
{
  "existing_cv": {
    "education": [
      {
        "school": "MIT",
        "major": "Computer Science",
        "start_date": "2018"
      }
    ],
    "experience": [
      {
        "title": "Developer",
        "company": "Meta",
        "start_date": "2021-06-01"
      }
    ],
    "certification": [
      {
        "certification_name": "AWS Developer",
        "organization": "Amazon"
      }
    ],
    "extra_curricular": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  },
  "new_data": {
    "education": [
      {
        "school": "Massachusetts Institute of Technology",
        "major": "Khoa học máy tính",
        "start_date": "2018",
        "degree": "Bachelor",
        "gpa": 3.9
      }
    ],
    "experience": [
      {
        "title": "Software Developer",
        "company": "Facebook Inc.",
        "start_date": "2021-06-01",
        "description": "Full-stack development"
      }
    ],
    "certification": [
      {
        "certification_name": "AWS Certified Developer Associate",
        "organization": "Amazon Web Services",
        "issue_date": "2023-01-15"
      }
    ],
    "extra_curricular": [],
    "language": [],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": []
  }
}
</script>

<script type="application/json" id="sampleFull">
{
  "existing_cv": {
    "education": [
      {
        "school": "HUST",
        "major": "Software Engineering", 
        "start_date": "2019"
      }
    ],
    "experience": [
      {
        "title": "Junior Developer",
        "company": "TechCorp",
        "start_date": "2023-01-01"
      }
    ],
    "certification": [
      {
        "certification_name": "PMP",
        "organization": "PMI"
      }
    ],
    "language": [
      {
        "language_name": "English",
        "level": "Intermediate"
      }
    ],
    "achievement": [],
    "reference": [],
    "external_doc": [],
    "summary": [],
    "hobby": [],
    "extra_curricular": []
  },
  "new_data": {
    "education": [
      {
        "school": "Hanoi University of Science and Technology",
        "major": "Kỹ thuật phần mềm",
        "start_date": "2019",
        "degree": "Bachelor",
        "gpa": 3.7
      }
    ],
    "experience": [
      {
        "title": "Junior Software Developer",
        "company": "TechCorp Ltd",
        "start_date": "2023-01-01",
        "end_date": "2024-01-01",
        "skills": ["React", "Node.js"]
      }
    ],
    "certification": [
      {
        "certification_name": "Project Management Professional",
        "organization": "Project Management Institute",
        "issue_date": "2023-03-15"
      }
    ],
    "language": [
      {
        "language_name": "English",
        "level": "Advanced"
      },
      {
        "language_name": "Vietnamese",
        "level": "Native"
      }
    ],
    "achievement": [
      {
        "title": "Best Graduate Award",
        "organization": "HUST",
        "issue_date": "2023-06-01"
      }
    ],
    "reference": [
      {
        "name": "John Smith",
        "email": "<EMAIL>",
        "position": "Senior Manager"
      }
    ],
    "external_doc": [],
    "summary": [],
    "hobby": [
      {
        "hobby": "Programming",
        "description": "Open source contributions"
      }
    ],
    "extra_curricular": [
      {
        "role": "President",
        "organization": "Computer Science Club",
        "start_date": "2021-01-01"
      }
    ]
  }
}
</script>
{% endblock %}

{% block scripts %}
<script>
// Initialize form validation and sample data loading
document.addEventListener('DOMContentLoaded', function() {
    // Load saved data from localStorage
    loadSavedData();
    
    // Setup JSON validation
    setupJsonValidation();
    
    // Setup form submission
    setupFormSubmission();
    
    // Scroll to results if they exist
    if (document.querySelector('#processedCvJson')) {
        setTimeout(() => {
            document.querySelector('#processedCvJson').closest('.row').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }, 100);
    }
});

// Copy to clipboard function
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const btn = element.parentElement.querySelector('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(function() {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}

// Copy results function
function copyResults() {
    {% if show_results and result %}
    const results = {
        action_summary: {{ result_action_summary | tojson }},
        section_summary: {{ result_section_summary | tojson }},
        detailed_actions: {{ enhanced_actions | tojson }},
        processed_cv: {{ result_processed_cv | tojson }}
    };
    
    navigator.clipboard.writeText(JSON.stringify(results, null, 2)).then(function() {
        // Show success feedback
        const btn = event.target;
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-check"></i> Copied!';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(function() {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
    {% endif %}
}
</script>
{% endblock %}