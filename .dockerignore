# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
*.egg-info
.venv
venv/
env/
ENV/

# Testing
.pytest_cache
.coverage
htmlcov/
.tox/
.cache

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment files (use docker-compose env instead)
.env
.env.local
.env.development
.env.test
.env.production

# Build artifacts
build/
dist/
*.tar.gz

# Documentation
README.md
docs/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Temporary files
tmp/
temp/
*.tmp