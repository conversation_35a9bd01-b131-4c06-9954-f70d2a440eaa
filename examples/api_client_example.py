#!/usr/bin/env python3
"""
Example API client for CV Duplicate Checker API

This script demonstrates how to interact with the CV Duplicate Checker API
using HTTP requests. It shows both successful and error scenarios.
"""

import requests
import sys

# API configuration
API_BASE_URL = "http://127.0.0.1:8000"


def check_api_health():
    """Check if the API is healthy and ready"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ API Health: {health_data['status']}")
            print(f"📦 Version: {health_data['version']}")
            print(f"🤖 Agent Ready: {health_data['agent_ready']}")
            return health_data['agent_ready']
        else:
            print(f"❌ API Health Check Failed: {response.status_code}")
            return False
    except requests.RequestException as e:
        print(f"❌ Failed to connect to API: {e}")
        return False


def example_cv_duplicate_check():
    """Example of CV duplicate checking with semantic matching"""
    
    # Sample data that demonstrates semantic matching
    request_data = {
        "existing_cv": {
            "education": [
                {
                    "school": "DHBK",
                    "major": "Khoa học máy tính",
                    "start_date": "2018"
                }
            ],
            "experience": [
                {
                    "title": "Software Engineer",
                    "company": "Tech Corp",
                    "start_date": "2022-01-01",
                    "description": "Phát triển ứng dụng web"
                }
            ],
            "certification": [
                {
                    "certification_name": "AWS Developer",
                    "organization": "Amazon"
                }
            ],
            "language": [
                {
                    "language_name": "English"
                }
            ],
            "extra_curricular": [],
            "achievement": [],
            "reference": [],
            "external_doc": [],
            "summary": [],
            "hobby": []
        },
        "new_data": {
            "education": [
                {
                    "school": "Đại học Bách Khoa",  # Semantic match with "DHBK"
                    "major": "Khoa học máy tính",
                    "start_date": "2018",
                    "end_date": "2022",
                    "degree": "Cử nhân",
                    "gpa": 3.5
                }
            ],
            "experience": [
                {
                    "title": "Software Engineer",
                    "company": "Tech Corp",
                    "start_date": "2022-01-01",
                    "description": "Phát triển ứng dụng web",
                    "end_date": "2023-12-31",
                    "skills": ["Python", "JavaScript"]
                },
                {
                    "title": "Senior Developer",
                    "company": "New Company",
                    "start_date": "2024-01-01",
                    "description": "Lead development team"
                }
            ],
            "certification": [
                {
                    "certification_name": "AWS Developer",
                    "organization": "Amazon",
                    "issue_date": "2023-01-01",
                    "expire_date": "2026-01-01"
                }
            ],
            "language": [
                {
                    "language_name": "English",
                    "level": "Advanced"
                },
                {
                    "language_name": "Japanese",
                    "level": "Intermediate"
                }
            ]
        }
    }
    
    print("🔍 Testing CV Duplicate Check with Semantic Matching...")
    print("📝 Example: DHBK will be semantically matched with 'Đại học Bách Khoa'")
    print()
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/check-duplicates",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ CV Processing Successful!")
            print()
            
            # Display action summary
            action_summary = result["action_summary"]
            print("📊 Action Summary:")
            print(f"   Total Actions: {action_summary['total_actions']}")
            print(f"   Added: {action_summary['added']}")
            print(f"   Updated: {action_summary['updated']}")
            print(f"   Skipped: {action_summary['skipped']}")
            print()
            
            # Display section summary
            print("📋 Section Summary:")
            for section, summary in result["section_summary"].items():
                if summary["added"] > 0 or summary["updated"] > 0 or summary["skipped"] > 0:
                    print(f"   {section}: +{summary['added']} ~{summary['updated']} ={summary['skipped']}")
            print()
            
            # Display detailed actions
            print("🔎 Detailed Actions:")
            for i, action in enumerate(result["detailed_actions"], 1):
                print(f"   {i}. {action['action'].upper()}: {action['section']}")
                print(f"      Reason: {action['reason']}")
                if action.get('fields_to_update'):
                    print(f"      Updated fields: {', '.join(action['fields_to_update'])}")
                print()
            
            # Show final CV structure
            print("📄 Final Processed CV Structure:")
            processed_cv = result["processed_cv"]
            for section, items in processed_cv.items():
                if items:
                    print(f"   {section}: {len(items)} items")
            
            return True
            
        else:
            print(f"❌ API Request Failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('message', 'Unknown error')}")
            except (ValueError, KeyError):
                print(f"Error response: {response.text}")
            return False
            
    except requests.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False


def example_error_handling():
    """Example of handling edge cases that should result in 0 actions"""
    print("🚨 Testing Edge Case - No Valid Data to Process...")
    
    # Edge case: existing CV with incomplete data, new data has nothing meaningful
    edge_case_data = {
        "existing_cv": {
            "education": [
                {
                    # Incomplete education entry
                    "school": "Some University",
                    "major": "Computer Science"
                    # Missing other important fields like start_date
                }
            ],
            "experience": [
                {
                    # Incomplete experience entry  
                    "title": "Developer"
                    # Missing company, start_date, etc.
                }
            ],
            "extra_curricular": [],
            "certification": [],
            "language": [],
            "achievement": [],
            "reference": [],
            "external_doc": [],
            "summary": [],
            "hobby": []
        },
        "new_data": {
            # New data is completely empty - should result in 0 actions
            "education": [],
            "experience": [],
            "extra_curricular": [],
            "certification": [],
            "language": [],
            "achievement": [],
            "reference": [],
            "external_doc": [],
            "summary": [],
            "hobby": []
        }
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/check-duplicates",
            json=edge_case_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            total_actions = result["action_summary"]["total_actions"]
            
            if total_actions == 0:
                print("✅ Edge case handling working correctly!")
                print(f"Total actions: {total_actions} (expected: 0)")
                print("Reason: No new data to process, so no actions were taken")
            else:
                print(f"❌ Expected 0 total actions, got {total_actions}")
                print("Action Summary:", result["action_summary"])
        else:
            print(f"❌ API request failed with status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('message', 'Unknown error')}")
            except (ValueError, KeyError):
                print(f"Error response: {response.text}")
            
    except requests.RequestException as e:
        print(f"❌ Request failed: {e}")


def main():
    """Main function to run all examples"""
    print("🚀 CV Duplicate Checker API Client Example")
    print("=" * 50)
    print()
    
    # Check API health first
    if not check_api_health():
        print("\n❌ API is not available. Please start the API server first:")
        print("   python main.py --reload")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Run CV duplicate check example
    success = example_cv_duplicate_check()
    
    print("\n" + "=" * 50)
    
    # Test edge case handling
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("✅ API Client Example Complete!")
    
    if success:
        print("\n📚 For more information:")
        print(f"   API Docs: {API_BASE_URL}/docs")
        print(f"   Health Check: {API_BASE_URL}/health")


if __name__ == "__main__":
    main()