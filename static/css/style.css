/* Custom CSS for CV Duplicate Checker Web Interface */

/* JSON Input Styling */
.json-input {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.json-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.json-input.is-valid {
    border-color: #198754;
}

.json-input.is-invalid {
    border-color: #dc3545;
}

/* JSON Validation Indicator */
.json-validation-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
}

.json-validation-indicator.valid {
    background-color: #198754;
}

.json-validation-indicator.valid::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.json-validation-indicator.invalid {
    background-color: #dc3545;
}

.json-validation-indicator.invalid::after {
    content: "✗";
    color: white;
    font-size: 12px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* JSON Output Styling */
.json-output {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
}

.json-output code {
    background: none;
    padding: 0;
    font-size: inherit;
    color: inherit;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 500;
}

/* Button Enhancements */
.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.btn.loading {
    position: relative;
}

.btn.loading .spinner-border {
    width: 1rem;
    height: 1rem;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

.item-details {
    max-width: 300px;
}

/* Badge Styling */
.badge {
    font-size: 0.75em;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Navigation Enhancements */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.2s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Footer */
footer {
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

/* Form Enhancements */
.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .json-input {
        font-size: 12px;
    }
    
    .json-output {
        font-size: 11px;
    }
    
    .item-details {
        max-width: 200px;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Animation for JSON validation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.json-validation-indicator.validating {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

.json-validation-indicator.validating::after {
    content: "...";
    color: white;
    font-size: 8px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Copy button styling */
.position-relative .btn {
    backdrop-filter: blur(10px);
}

/* Syntax highlighting for JSON */
.json-key {
    color: #0066cc;
    font-weight: bold;
}

.json-string {
    color: #008800;
}

.json-number {
    color: #cc6600;
}

.json-boolean {
    color: #990099;
}

.json-null {
    color: #999999;
}

/* Health status indicators */
.health-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.health-indicator.healthy {
    background-color: #198754;
    box-shadow: 0 0 8px rgba(25, 135, 84, 0.5);
}

.health-indicator.unhealthy {
    background-color: #dc3545;
    box-shadow: 0 0 8px rgba(220, 53, 69, 0.5);
}

/* Collapsible sections */
.collapse .card-body {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* Custom scrollbar for JSON outputs */
.json-output::-webkit-scrollbar {
    width: 8px;
}

.json-output::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.json-output::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.json-output::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Tooltips */
[data-bs-toggle="tooltip"] {
    cursor: help;
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Print styles */
@media print {
    .navbar,
    footer,
    .btn,
    .btn-group {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .json-output {
        font-size: 10px;
        max-height: none;
    }
}