# CV Duplicate Checker Agent - Architecture Diagram

## Overall System Architecture

```mermaid
graph TB
    subgraph InputLayer ["Input Layer"]
        A[Existing CV JSON] 
        B[New Data JSON]
    end
    
    subgraph CVCheckerAgent ["CV Duplicate Checker Agent"]
        C[CVDuplicateCheckerAgent]
        D[ChatOpenAI LLM<br/>gpt-4o-mini]
        E[LangGraph StateGraph]
    end
    
    subgraph ProcessingLayer ["Processing Layer"]
        F[Parallel Section Processing]
        G[Semantic Matching Engine]
        H[Duplicate Detection Logic]
    end
    
    subgraph OutputLayer ["Output Layer"]
        I[Processed CV]
        J[Action Summary]
        K[Detailed Actions]
    end
    
    A --> C
    B --> C
    C --> D
    C --> E
    E --> F
    F --> G
    G --> D
    G --> H
    H --> I
    H --> J
    H --> K
```

## LangGraph Workflow Architecture

```mermaid
graph TD
    START([START]) --> PI[parse_input]
    
    PI --> CE[check_education]
    PI --> CX[check_experience] 
    PI --> CC[check_extra_curricular]
    PI --> CT[check_certification]
    PI --> CL[check_language]
    PI --> CA[check_achievement]
    PI --> CR[check_reference]
    PI --> CD[check_external_doc]
    PI --> CS[check_summary]
    PI --> CH[check_hobby]
    
    CE --> MR[merge_results]
    CX --> MR
    CC --> MR
    CT --> MR
    CL --> MR
    CA --> MR
    CR --> MR
    CD --> MR
    CS --> MR
    CH --> MR
    
    MR --> END([END])
    
    subgraph ParallelProcessing ["Parallel Processing"]
        CE
        CX
        CC
        CT
        CL
        CA
        CR
        CD
        CS
        CH
    end
    
    style PI fill:#e1f5fe
    style MR fill:#f3e5f5
```

## State Management Architecture

```mermaid
graph LR
    subgraph AgentStateStructure ["AgentState Structure"]
        A[existing_cv: Dict]
        B[new_data: Dict]
        C[processed_cv: Dict]
        D[analysis_results: List]
        E[final_result: Optional Dict]
        
        subgraph SectionResults ["Section Results"]
            F[education_result]
            G[experience_result]
            H[extra_curricular_result]
            I[certification_result]
            J[language_result]
            K[achievement_result]
            L[reference_result]
            M[external_doc_result]
            N[summary_result]
            O[hobby_result]
        end
    end
    
    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#e1f5fe
```

## Semantic Matching Engine Workflow

```mermaid
flowchart TD
    A[Compare Two Values] --> B{Exact Match?}
    
    B -->|Yes| C[Return True]
    B -->|No| D{Text Fields?}
    
    D -->|No| E[Use Exact Comparison]
    D -->|Yes| F[Determine Field Type]
    
    F --> G{Field Type}
    G -->|school| H[School Context Prompt]
    G -->|company| I[Company Context Prompt]
    G -->|certification| J[Certification Context Prompt]
    G -->|major| K[Major/Field Context Prompt]
    G -->|position| L[Position Context Prompt]
    G -->|general| M[General Context Prompt]
    
    H --> N[LLM Semantic Analysis]
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
    
    N --> O{LLM Response}
    O -->|"true"| P[Return True - Semantic Match]
    O -->|"false"| Q[Return False - No Match]
    O -->|Error| R[Fallback to Exact Match]
    
    E --> S[Return Comparison Result]
    P --> T[Log Semantic Match Found]
    Q --> U[Log No Semantic Match]
    R --> V[Log LLM Error, Use Fallback]
    
    style N fill:#e3f2fd
    style P fill:#e8f5e8
    style Q fill:#ffebee
    style R fill:#fff3e0
```

## Duplicate Detection Logic Flow

```mermaid
flowchart TD
    A[Process Section Items] --> B[For Each New Item]
    
    B --> C[For Each Existing Item]
    C --> D[Check Duplicate Criteria]
    
    D --> E{All Criteria Match?}
    E -->|Yes| F[Duplicate Found!]
    E -->|No| G[Continue to Next Existing Item]
    
    G --> H{More Existing Items?}
    H -->|Yes| C
    H -->|No| I[No Duplicate Found]
    
    F --> J[Check Updatable Fields]
    J --> K{Missing Fields in Existing?}
    
    K -->|Yes| L[Action: UPDATE<br/>Add missing fields]
    K -->|No| M[Action: SKIP<br/>Complete duplicate]
    
    I --> N[Action: ADD<br/>New information]
    
    L --> O[Apply Changes to Data]
    M --> P[Log Skip Reason]
    N --> Q[Add to Collection]
    
    O --> R[Continue Processing]
    P --> R
    Q --> R
    
    style F fill:#fff3e0
    style L fill:#e3f2fd
    style M fill:#ffebee
    style N fill:#e8f5e8
```

## CV Section Processing Details

```mermaid
graph TD
    subgraph EducationProcessing ["Education Processing"]
        E1[Duplicate Criteria:<br/>school + major]
        E2[Updatable Fields:<br/>start_date, end_date,<br/>degree, description,<br/>favorite_subjects, gpa]
    end
    
    subgraph ExperienceProcessing ["Experience Processing"]
        X1[Duplicate Criteria:<br/>title + company + start_date]
        X2[Updatable Fields:<br/>description, end_date, skills]
    end
    
    subgraph CertificationProcessing ["Certification Processing"]
        C1[Duplicate Criteria:<br/>certification_name + organization]
        C2[Updatable Fields:<br/>issue_date, expire_date, url]
    end
    
    subgraph LanguageProcessing ["Language Processing"]
        L1[Duplicate Criteria:<br/>language_name]
        L2[Updatable Fields:<br/>level]
    end
    
    subgraph OtherSections ["Other Sections"]
        O1[Achievement: title + organization + issue_date]
        O2[Reference: email]
        O3[External Doc: link]
        O4[Summary: summary text]
        O5[Hobby: hobby name]
    end
    
    style E1 fill:#e3f2fd
    style X1 fill:#e8f5e8
    style C1 fill:#fff3e0
    style L1 fill:#f3e5f5
```

## Action Types and Outcomes

```mermaid
graph LR
    subgraph ActionTypes ["Action Types"]
        A1[ADD<br/>New Information]
        A2[UPDATE<br/>Supplement Missing Fields]
        A3[SKIP<br/>Complete Duplicate]
    end
    
    subgraph SemanticDetection ["Semantic Detection"]
        S1[Exact Match Detection]
        S2[LLM Semantic Match]
        S3[Abbreviation Recognition]
        S4[Translation Matching]
    end
    
    subgraph FinalOutput ["Final Output"]
        O1[Processed CV<br/>Merged Data]
        O2[Action Summary<br/>Counts by Type]
        O3[Section Summary<br/>Per-section Stats]
        O4[Detailed Actions<br/>Step-by-step Log]
    end
    
    A1 --> O1
    A2 --> O1
    A3 --> O1
    
    S1 --> A1
    S1 --> A2
    S1 --> A3
    
    S2 --> A2
    S3 --> A2
    S4 --> A2
    
    O1 --> Result[Final JSON Output]
    O2 --> Result
    O3 --> Result
    O4 --> Result
    
    style S2 fill:#e3f2fd
    style A2 fill:#fff3e0
    style Result fill:#e8f5e8
```

## Technology Stack

```mermaid
graph TB
    subgraph CoreTechnologies ["Core Technologies"]
        A[Python 3.13+]
        B[LangChain 0.3.25+]
        C[LangGraph 0.4.8+]
        D[OpenAI API]
    end
    
    subgraph DataProcessing ["Data Processing"]
        E[Pydantic Models]
        F[JSON Serialization]
        G[Type Validation]
    end
    
    subgraph AIComponents ["AI Components"]
        H[ChatOpenAI<br/>gpt-4o-mini]
        I[System Prompts]
        J[Context-aware Matching]
    end
    
    subgraph StateManagement ["State Management"]
        K[TypedDict State]
        L[Section-specific Results]
        M[Conflict-free Updates]
    end
    
    A --> E
    B --> H
    C --> K
    D --> I
    
    E --> F
    F --> G
    
    H --> I
    I --> J
    
    K --> L
    L --> M
    
    style H fill:#e3f2fd
    style J fill:#fff3e0
    style M fill:#e8f5e8
```

## Performance Characteristics

```mermaid
graph TD
    subgraph ParallelBenefits ["Parallel Processing Benefits"]
        P1[All 10 CV sections<br/>process simultaneously]
        P2[Reduced total processing time]
        P3[Better resource utilization]
    end
    
    subgraph LLMOptimization ["LLM Usage Optimization"]
        L1[Only calls LLM for text fields]
        L2[Exact match tried first]
        L3[Context-specific prompts]
        L4[Graceful fallback handling]
    end
    
    subgraph MemoryEfficiency ["Memory Efficiency"]
        M1[Section-specific state keys]
        M2[Avoid shared state conflicts]
        M3[Minimal state copying]
    end
    
    P1 --> Performance[High Performance]
    P2 --> Performance
    P3 --> Performance
    
    L1 --> Efficiency[Cost Efficient]
    L2 --> Efficiency
    L3 --> Efficiency
    L4 --> Efficiency
    
    M1 --> Reliability[Reliable]
    M2 --> Reliability
    M3 --> Reliability
    
    style Performance fill:#e8f5e8
    style Efficiency fill:#e3f2fd
    style Reliability fill:#fff3e0
```

## Example Semantic Matching Cases

```mermaid
graph LR
    subgraph SchoolExamples ["School Examples"]
        S1["DHBK <--> Đại học Bách Khoa"]
        S2["MIT <--> Massachusetts Institute of Technology"]
        S3["HUST <--> Hanoi University of Science and Technology"]
    end
    
    subgraph CompanyExamples ["Company Examples"]
        C1["Google <--> Google LLC"]
        C2["Microsoft <--> Microsoft Corporation"]
        C3["Meta <--> Facebook Inc."]
    end
    
    subgraph CertificationExamples ["Certification Examples"]
        T1["AWS Developer <--> AWS Certified Developer Associate"]
        T2["PMP <--> Project Management Professional"]
    end
    
    subgraph MajorExamples ["Major Examples"]
        M1["Computer Science <--> Khoa học máy tính"]
        M2["Business Administration <--> Quản trị kinh doanh"]
    end
    
    S1 --> LLM[LLM Semantic Engine]
    S2 --> LLM
    S3 --> LLM
    C1 --> LLM
    C2 --> LLM
    C3 --> LLM
    T1 --> LLM
    T2 --> LLM
    M1 --> LLM
    M2 --> LLM
    
    LLM --> Match[Semantic Match Detected]
    
    style LLM fill:#e3f2fd
    style Match fill:#e8f5e8
```