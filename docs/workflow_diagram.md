# CV Duplicate Checker - Main Workflow Diagram

## High-Level Agent Workflow

```mermaid
flowchart TD
    A[📄 Existing CV JSON] --> D[🤖 CV Duplicate Checker Agent]
    B[📄 New Data JSON] --> D
    
    D --> E[🔄 Parse Input & Initialize State]
    E --> F[⚡ Parallel Section Processing]
    
    F --> G1[📚 Education]
    F --> G2[💼 Experience] 
    F --> G3[🎯 Extra-curricular]
    F --> G4[📜 Certification]
    F --> G5[🌍 Language]
    F --> G6[🏆 Achievement]
    F --> G7[📞 Reference]
    F --> G8[📎 External Doc]
    F --> G9[📝 Summary]
    F --> G10[🎨 Hobby]
    
    G1 --> H[🔍 Semantic Duplicate Detection]
    G2 --> H
    G3 --> H
    G4 --> H
    G5 --> H
    G6 --> H
    G7 --> H
    G8 --> H
    G9 --> H
    G10 --> H
    
    H --> I[🧠 LLM Analysis]
    I --> J[📊 Merge Results]
    J --> K[📋 Final Output]
    
    style D fill:#e3f2fd
    style F fill:#fff3e0
    style H fill:#f3e5f5
    style I fill:#e8f5e8
    style K fill:#ffebee
```

## Semantic Matching Decision Tree

```mermaid
flowchart TD
    A[🔄 Compare Two Values] --> B{📝 Exact Match?}
    
    B -->|✅ Yes| C[✨ Return True]
    B -->|❌ No| D{📝 Text Fields?}
    
    D -->|❌ No| E[🔍 Exact Comparison Only]
    D -->|✅ Yes| F[🎯 Determine Field Context]
    
    F --> G[🧠 Call LLM with Context]
    
    subgraph FieldContexts ["🎓 Field Contexts"]
        H1[🏫 School Context<br/>DHBK <--> Đại học Bách Khoa]
        H2[🏢 Company Context<br/>Google <--> Google LLC]
        H3[📜 Certification Context<br/>AWS <--> AWS Certified Developer]
        H4[📚 Major Context<br/>CS <--> Khoa học máy tính]
    end
    
    G --> I{🤔 LLM Decision}
    I -->|"true"| J[✅ Semantic Match Found]
    I -->|"false"| K[❌ No Semantic Match]
    I -->|⚠️ Error| L[🔄 Fallback to Exact Match]
    
    J --> M[📝 Log: Detected by AI]
    K --> N[📝 Log: Different entities]
    L --> O[📝 Log: AI unavailable]
    
    style G fill:#e3f2fd
    style J fill:#e8f5e8
    style K fill:#ffebee
    style L fill:#fff3e0
```

## Action Decision Flow

```mermaid
flowchart TD
    A[🔍 Item Comparison] --> B{🎯 Duplicate Found?}
    
    B -->|❌ No| C[➕ ADD Action<br/>New Information]
    B -->|✅ Yes| D{📝 Missing Fields?}
    
    D -->|✅ Yes| E[🔄 UPDATE Action<br/>Supplement Info]
    D -->|❌ No| F[⏭️ SKIP Action<br/>Complete Duplicate]
    
    C --> G[📊 Action Applied]
    E --> G
    F --> G
    
    G --> H[📋 Log Action Details]
    H --> I{🔄 More Items?}
    
    I -->|✅ Yes| A
    I -->|❌ No| J[✨ Section Complete]
    
    style C fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#ffebee
    style J fill:#f3e5f5
```

## Real Example: School Matching

```mermaid
flowchart LR
    A["🎓 Existing: DHBK"] --> B[🤖 Semantic Matcher]
    C["🎓 New: Đại học Bách Khoa"] --> B
    
    B --> D[🧠 LLM Analysis]
    D --> E["💭 'These refer to the same<br/>technical university in Vietnam'"]
    E --> F[✅ MATCH FOUND]
    
    F --> G[🔄 UPDATE Action]
    G --> H["📝 Result: Add degree, GPA<br/>to existing DHBK entry"]
    
    style B fill:#e3f2fd
    style D fill:#fff3e0
    style F fill:#e8f5e8
    style G fill:#f3e5f5
```

## System Integration Overview

```mermaid
graph TB
    subgraph TechStack ["🔧 Technology Stack"]
        A[🐍 Python 3.13+]
        B[🔗 LangChain 0.3.25+]
        C[📊 LangGraph 0.4.8+]
        D[🤖 OpenAI GPT-4o-mini]
    end
    
    subgraph DataFlow ["📊 Data Flow"]
        E[📥 JSON Input]
        F[🔄 Pydantic Validation]
        G[⚡ Parallel Processing]
        H[📤 JSON Output]
    end
    
    subgraph AIProcessing ["🧠 AI Processing"]
        I[🎯 Context-aware Prompts]
        J[🔍 Semantic Analysis]
        K[✨ Smart Matching]
    end
    
    A --> E
    B --> I
    C --> G
    D --> J
    
    E --> F
    F --> G
    G --> H
    
    I --> J
    J --> K
    K --> H
    
    style G fill:#fff3e0
    style J fill:#e3f2fd
    style H fill:#e8f5e8
```

## Performance & Benefits

```mermaid
graph TD
    subgraph Performance ["⚡ Performance"]
        P1[🚀 Parallel Processing<br/>All sections simultaneously]
        P2[🎯 Smart LLM Usage<br/>Only when needed]
        P3[💾 Efficient Memory<br/>Section-specific state]
    end
    
    subgraph Accuracy ["🎯 Accuracy"]
        A1[🧠 Semantic Understanding<br/>Beyond exact matching]
        A2[🌍 Multi-language Support<br/>Vietnamese ↔ English]
        A3[📝 Abbreviation Recognition<br/>MIT ↔ Full names]
    end
    
    subgraph Reliability ["🛡️ Reliability"]
        R1[🔄 Graceful Fallbacks<br/>If LLM fails]
        R2[📊 Detailed Logging<br/>Full audit trail]
        R3[✅ Comprehensive Testing<br/>13 test scenarios]
    end
    
    P1 --> Result[🎉 Production Ready]
    P2 --> Result
    P3 --> Result
    A1 --> Result
    A2 --> Result
    A3 --> Result
    R1 --> Result
    R2 --> Result
    R3 --> Result
    
    style Result fill:#e8f5e8
```